// 需求来源: 工作计划书.md 3.1 事件系统实现
// 事件系统的单元测试和功能验证

using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Simulation.Core.Events;

namespace Simulation.Tests.Core
{
    /// <summary>
    /// 事件系统单元测试
    /// 使用Unity TestFramework验证事件发布、订阅、取消订阅的正确性
    /// </summary>
    [TestFixture]
    public class EventSystemTests
    {
        private int syncEventCount = 0;
        private int asyncEventCount = 0;
        private CancellationTokenSource cancellationTokenSource;
        private GameObject testGameObject;

        [SetUp]
        public void SetUp()
        {
            // 每个测试前清理事件系统
            EventManager.ClearAllSubscriptions();
            syncEventCount = 0;
            asyncEventCount = 0;
            cancellationTokenSource = new CancellationTokenSource();
            testGameObject = new GameObject("TestObject");
        }

        [TearDown]
        public void TearDown()
        {
            // 每个测试后清理资源
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
            EventManager.ClearAllSubscriptions();
            if (testGameObject != null)
            {
                UnityEngine.Object.DestroyImmediate(testGameObject);
            }
        }

        /// <summary>
        /// 测试基本事件发布和订阅
        /// </summary>
        [Test]
        public void TestBasicEventPublishSubscribe()
        {
            // Arrange
            syncEventCount = 0;
            EventManager.Subscribe<SimulationStartedEvent>(OnSimulationStarted);
            
            // Act
            var startEvent = new SimulationStartedEvent(testGameObject, "测试模拟");
            EventManager.Publish(startEvent);
            
            // Assert
            Assert.AreEqual(1, syncEventCount, "基本事件发布和订阅应该触发一次事件处理");
            
            // Cleanup
            EventManager.Unsubscribe<SimulationStartedEvent>(OnSimulationStarted);
        }

        /// <summary>
        /// 测试异步事件处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestAsyncEventHandling()
        {
            // Arrange
            asyncEventCount = 0;
            EventManager.SubscribeAsync<EntityStateChangedEvent>(OnEntityStateChangedAsync);
            
            // Act
            var stateEvent = new EntityStateChangedEvent(testGameObject, Guid.NewGuid(), "Idle", "Working");
            var publishTask = EventManager.PublishAsync(stateEvent, cancellationTokenSource.Token);
            
            // Wait for async completion
            yield return publishTask.ToCoroutine();
            
            // Assert
            Assert.AreEqual(1, asyncEventCount, "异步事件处理应该触发一次事件处理");
            
            // Cleanup
            EventManager.UnsubscribeAsync<EntityStateChangedEvent>(OnEntityStateChangedAsync);
        }

        /// <summary>
        /// 测试事件队列和优先级
        /// </summary>
        [Test]
        public void TestEventQueueAndPriority()
        {
            // Arrange
            var processedEvents = new List<string>();
            EventManager.Subscribe<SimulationStartedEvent>(e => processedEvents.Add("High"));
            EventManager.Subscribe<EntityStateChangedEvent>(e => processedEvents.Add("Medium"));
            EventManager.Subscribe<DebugEvent>(e => processedEvents.Add("Low"));
            
            // Act - 以相反的优先级顺序加入队列
            EventManager.PublishQueued(new DebugEvent(testGameObject, "低优先级"));
            EventManager.PublishQueued(new EntityStateChangedEvent(testGameObject, Guid.NewGuid(), "A", "B"));
            EventManager.PublishQueued(new SimulationStartedEvent(testGameObject, "高优先级"));
            
            EventManager.ProcessQueuedEvents();
            
            // Assert - 验证优先级顺序
            Assert.AreEqual(3, processedEvents.Count, "应该处理3个事件");
            Assert.AreEqual("High", processedEvents[0], "第一个应该是高优先级事件");
            Assert.AreEqual("Medium", processedEvents[1], "第二个应该是中等优先级事件");
            Assert.AreEqual("Low", processedEvents[2], "第三个应该是低优先级事件");
        }

        /// <summary>
        /// 测试事件取消订阅
        /// </summary>
        [Test]
        public void TestEventUnsubscribe()
        {
            // Arrange
            syncEventCount = 0;
            EventManager.Subscribe<DebugEvent>(OnDebugEvent);
            
            // Act - 发布第一个事件
            EventManager.Publish(new DebugEvent(testGameObject, "第一次"));
            
            // 取消订阅
            EventManager.Unsubscribe<DebugEvent>(OnDebugEvent);
            
            // 发布第二个事件
            EventManager.Publish(new DebugEvent(testGameObject, "第二次"));
            
            // Assert - 验证只收到第一次事件
            Assert.AreEqual(1, syncEventCount, "取消订阅后不应该再收到事件");
        }

        /// <summary>
        /// 测试错误处理
        /// </summary>
        [Test]
        public void TestErrorHandling()
        {
            // Arrange
            EventManager.Subscribe<ErrorEvent>(ThrowExceptionHandler);
            
            // Act & Assert - 发布事件应该捕获异常而不崩溃
            Assert.DoesNotThrow(() => 
            {
                EventManager.Publish(new ErrorEvent(testGameObject, "测试错误"));
            }, "事件系统应该捕获处理器中的异常而不崩溃");
            
            // Cleanup
            EventManager.Unsubscribe<ErrorEvent>(ThrowExceptionHandler);
        }

        /// <summary>
        /// 测试多个订阅者
        /// </summary>
        [Test]
        public void TestMultipleSubscribers()
        {
            // Arrange
            int subscriber1Count = 0;
            int subscriber2Count = 0;
            
            EventManager.Subscribe<DebugEvent>(e => subscriber1Count++);
            EventManager.Subscribe<DebugEvent>(e => subscriber2Count++);
            
            // Act
            EventManager.Publish(new DebugEvent(testGameObject, "测试消息"));
            
            // Assert
            Assert.AreEqual(1, subscriber1Count, "订阅者1应该收到事件");
            Assert.AreEqual(1, subscriber2Count, "订阅者2应该收到事件");
        }
        
        /// <summary>
        /// 测试分帧处理队列事件
        /// </summary>
        [Test]
        public void TestFramedEventProcessing()
        {
            // Arrange
            var processedEvents = new List<string>();
            EventManager.Subscribe<DebugEvent>(e => processedEvents.Add(e.Message));
            
            // 添加多个事件到队列
            for (int i = 0; i < 5; i++)
            {
                EventManager.PublishQueued(new DebugEvent(testGameObject, $"事件{i}"));
            }
            
            // Act - 分两帧处理
            EventManager.ProcessQueuedEvents(3); // 处理前3个
            Assert.AreEqual(3, processedEvents.Count, "第一帧应该处理3个事件");
            
            EventManager.ProcessQueuedEvents(3); // 处理剩余的
            Assert.AreEqual(5, processedEvents.Count, "第二帧应该处理完所有事件");
        }

        // 事件处理器
        private void OnSimulationStarted(SimulationStartedEvent e)
        {
            syncEventCount++;
            Debug.Log($"收到模拟开始事件: {e.SimulationName}");
        }

        private async UniTask OnEntityStateChangedAsync(EntityStateChangedEvent e)
        {
            await UniTask.Delay(100); // 模拟异步处理
            asyncEventCount++;
            Debug.Log($"异步处理实体状态变化: {e.PreviousState} -> {e.NewState}");
        }

        private void OnDebugEvent(DebugEvent e)
        {
            syncEventCount++;
            Debug.Log($"收到调试事件: {e.Message}");
        }

        private void ThrowExceptionHandler(ErrorEvent e)
        {
            throw new System.Exception("测试异常处理");
        }
        
        /// <summary>
        /// 测试取消令牌功能
        /// </summary>
        [Test]
        public async UniTaskVoid TestCancellationTokenAsync()
        {
            Debug.Log("Start Test CancellationTokenAsync");
            
            // Arrange
            asyncEventCount = 0;
            var cts = new CancellationTokenSource();
            EventManager.SubscribeAsync<EntityStateChangedEvent>(OnEntityStateChangedAsync);
            
            // Act - 立即取消
            cts.Cancel();
            var stateEvent = new EntityStateChangedEvent(testGameObject, Guid.NewGuid(), "Idle", "Working");
            
            bool operationCanceled = false;
            try
            {
                await EventManager.PublishAsync(stateEvent, cts.Token);
            }
            catch (OperationCanceledException)
            {
                operationCanceled = true;
            }
            
            // Assert
            Assert.IsTrue(operationCanceled, "应该抛出OperationCanceledException");
            
            // Cleanup
            EventManager.UnsubscribeAsync<EntityStateChangedEvent>(OnEntityStateChangedAsync);
            cts.Dispose();
        }
        
        /// <summary>
        /// 测试事件属性
        /// </summary>
        [Test]
        public void TestEventProperties()
        {
            // Arrange & Act
            var simulationEvent = new SimulationStartedEvent(testGameObject, "测试模拟");
            var entityEvent = new EntityStateChangedEvent(testGameObject, Guid.NewGuid(), "Idle", "Working");
            var debugEvent = new DebugEvent(testGameObject, "测试消息", "测试分类");
            
            // Assert
            Assert.IsNotNull(simulationEvent.EventId, "事件应该有唯一ID");
            Assert.Greater(simulationEvent.UnityTimestamp, 0, "Unity时间戳应该大于0");
            Assert.IsNotNull(simulationEvent.SystemTimestamp, "系统时间戳不应该为空");
            Assert.AreEqual("测试模拟", simulationEvent.SimulationName, "模拟名称应该正确");
            
            Assert.AreEqual(100, simulationEvent.Priority, "模拟事件应该是高优先级");
            Assert.AreEqual(50, entityEvent.Priority, "实体事件应该是中等优先级");
            Assert.AreEqual(1, debugEvent.Priority, "调试事件应该是低优先级");
            
            Assert.AreEqual("SimulationStartedEvent", simulationEvent.EventType, "事件类型名称应该正确");
        }

        /// <summary>
        /// 测试事件过滤器
        /// </summary>
        [Test]
        public void TestEventFilters()
        {
            // Arrange
            syncEventCount = 0;
            EventManager.Subscribe<DebugEvent>(OnDebugEvent);

            // 添加类型过滤器，只允许DebugEvent
            var typeFilter = new EventTypeFilter(new[] { typeof(DebugEvent) }, true);
            EventManager.AddEventFilter(typeFilter);

            // Act
            EventManager.Publish(new DebugEvent(testGameObject, "应该通过"));
            EventManager.Publish(new ErrorEvent(testGameObject, "应该被过滤"));

            // Assert
            Assert.AreEqual(1, syncEventCount, "类型过滤器应该只允许DebugEvent通过");

            // Cleanup
            EventManager.ClearEventFilters();
            EventManager.Unsubscribe<DebugEvent>(OnDebugEvent);
        }

        /// <summary>
        /// 测试事件统计功能
        /// </summary>
        [Test]
        public void TestEventStatistics()
        {
            // Arrange
            EventManager.SetPerformanceMonitoring(true);
            EventManager.Subscribe<DebugEvent>(OnDebugEvent);

            // Act
            EventManager.Publish(new DebugEvent(testGameObject, "统计测试1"));
            EventManager.Publish(new DebugEvent(testGameObject, "统计测试2"));

            // Assert
            var stats = EventManager.GetEventStatistics<DebugEvent>();
            Assert.AreEqual(2, stats.TotalPublished, "应该记录2次发布");
            Assert.AreEqual(2, stats.TotalProcessed, "应该记录2次处理");

            // Cleanup
            EventManager.Unsubscribe<DebugEvent>(OnDebugEvent);
            EventManager.SetPerformanceMonitoring(false);
        }

        /// <summary>
        /// 测试订阅者计数
        /// </summary>
        [Test]
        public void TestSubscriberCount()
        {
            // Arrange & Act
            Assert.AreEqual(0, EventManager.GetSubscriberCount<DebugEvent>(), "初始订阅者数量应该为0");

            EventManager.Subscribe<DebugEvent>(OnDebugEvent);
            Assert.AreEqual(1, EventManager.GetSubscriberCount<DebugEvent>(), "添加一个订阅者后数量应该为1");

            EventManager.Subscribe<DebugEvent>(OnDebugEvent2);
            Assert.AreEqual(2, EventManager.GetSubscriberCount<DebugEvent>(), "添加第二个订阅者后数量应该为2");

            // Cleanup
            EventManager.Unsubscribe<DebugEvent>(OnDebugEvent);
            EventManager.Unsubscribe<DebugEvent>(OnDebugEvent2);
        }

        /// <summary>
        /// 测试队列事件数量
        /// </summary>
        [Test]
        public void TestQueuedEventCount()
        {
            // Arrange & Act
            Assert.AreEqual(0, EventManager.GetQueuedEventCount(), "初始队列应该为空");

            EventManager.PublishQueued(new DebugEvent(testGameObject, "队列测试1"));
            EventManager.PublishQueued(new DebugEvent(testGameObject, "队列测试2"));

            Assert.AreEqual(2, EventManager.GetQueuedEventCount(), "队列中应该有2个事件");

            EventManager.ProcessQueuedEvents();
            Assert.AreEqual(0, EventManager.GetQueuedEventCount(), "处理后队列应该为空");
        }

        // 额外的事件处理器用于测试
        private void OnDebugEvent2(DebugEvent e)
        {
            syncEventCount++;
        }
    }
}
