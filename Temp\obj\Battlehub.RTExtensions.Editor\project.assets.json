{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Battlehub.LoadImageAsync/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Battlehub.LoadImageAsync.dll": {}}, "runtime": {"bin/placeholder/Battlehub.LoadImageAsync.dll": {}}}, "Battlehub.RTEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.LoadImageAsync": "1.0.0", "Battlehub.Storage.Addressables": "1.0.0", "Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0", "HSVPicker": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTEditor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTEditor.dll": {}}}, "Battlehub.RTExtensions/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.LoadImageAsync": "1.0.0", "Battlehub.RTEditor": "1.0.0", "Battlehub.Storage.Addressables": "1.0.0", "Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0", "HSVPicker": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTExtensions.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTExtensions.dll": {}}}, "Battlehub.Storage.Addressables/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Addressables.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Addressables.dll": {}}}, "Battlehub.Storage.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Core.Editor.dll": {}}}, "Battlehub.Storage.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Core.Runtime.dll": {}}}, "Battlehub.Storage.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Editor": "1.0.0", "Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Editor": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Editor.dll": {}}}, "Battlehub.Storage.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Runtime.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Runtime.dll": {}}}, "Battlehub.Storage.ShaderUtil.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Editor.dll": {}}}, "Battlehub.Storage.ShaderUtil.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Runtime.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Runtime.dll": {}}}, "HSVPicker/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/HSVPicker.dll": {}}, "runtime": {"bin/placeholder/HSVPicker.dll": {}}}}}, "libraries": {"Battlehub.LoadImageAsync/1.0.0": {"type": "project", "path": "Battlehub.LoadImageAsync.csproj", "msbuildProject": "Battlehub.LoadImageAsync.csproj"}, "Battlehub.RTEditor/1.0.0": {"type": "project", "path": "Battlehub.RTEditor.csproj", "msbuildProject": "Battlehub.RTEditor.csproj"}, "Battlehub.RTExtensions/1.0.0": {"type": "project", "path": "Battlehub.RTExtensions.csproj", "msbuildProject": "Battlehub.RTExtensions.csproj"}, "Battlehub.Storage.Addressables/1.0.0": {"type": "project", "path": "Battlehub.Storage.Addressables.csproj", "msbuildProject": "Battlehub.Storage.Addressables.csproj"}, "Battlehub.Storage.Core.Editor/1.0.0": {"type": "project", "path": "Battlehub.Storage.Core.Editor.csproj", "msbuildProject": "Battlehub.Storage.Core.Editor.csproj"}, "Battlehub.Storage.Core.Runtime/1.0.0": {"type": "project", "path": "Battlehub.Storage.Core.Runtime.csproj", "msbuildProject": "Battlehub.Storage.Core.Runtime.csproj"}, "Battlehub.Storage.Editor/1.0.0": {"type": "project", "path": "Battlehub.Storage.Editor.csproj", "msbuildProject": "Battlehub.Storage.Editor.csproj"}, "Battlehub.Storage.Runtime/1.0.0": {"type": "project", "path": "Battlehub.Storage.Runtime.csproj", "msbuildProject": "Battlehub.Storage.Runtime.csproj"}, "Battlehub.Storage.ShaderUtil.Editor/1.0.0": {"type": "project", "path": "Battlehub.Storage.ShaderUtil.Editor.csproj", "msbuildProject": "Battlehub.Storage.ShaderUtil.Editor.csproj"}, "Battlehub.Storage.ShaderUtil.Runtime/1.0.0": {"type": "project", "path": "Battlehub.Storage.ShaderUtil.Runtime.csproj", "msbuildProject": "Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "HSVPicker/1.0.0": {"type": "project", "path": "HSVPicker.csproj", "msbuildProject": "HSVPicker.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Battlehub.LoadImageAsync >= 1.0.0", "Battlehub.RTEditor >= 1.0.0", "Battlehub.RTExtensions >= 1.0.0", "Battlehub.Storage.Addressables >= 1.0.0", "Battlehub.Storage.Core.Editor >= 1.0.0", "Battlehub.Storage.Core.Runtime >= 1.0.0", "Battlehub.Storage.Editor >= 1.0.0", "Battlehub.Storage.Runtime >= 1.0.0", "Battlehub.Storage.ShaderUtil.Editor >= 1.0.0", "Battlehub.Storage.ShaderUtil.Runtime >= 1.0.0", "HSVPicker >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\VS2022Tools\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj", "projectName": "Battlehub.RTExtensions.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTExtensions.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}