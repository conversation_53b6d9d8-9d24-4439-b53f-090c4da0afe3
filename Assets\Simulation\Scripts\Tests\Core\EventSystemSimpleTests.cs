using NUnit.Framework;
using UnityEngine;
using Simulation.Core.Events;

namespace Simulation.Tests.Core
{
    [TestFixture]
    public class EventSystemSimpleTests
    {
        private GameObject testGameObject;
        private int eventCount;

        [SetUp]
        public void SetUp()
        {
            testGameObject = new GameObject("TestObject");
            eventCount = 0;
            EventManager.ClearAllSubscriptions();
        }

        [TearDown]
        public void TearDown()
        {
            EventManager.ClearAllSubscriptions();
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
        }

        #region 基本功能测试

        [Test]
        public void Subscribe_ShouldAddEventListener()
        {
            // Arrange & Act
            EventManager.Subscribe<TestEvent>(OnTestEvent);

            // Assert
            Assert.AreEqual(1, EventManager.GetSubscriberCount<TestEvent>());
        }

        [Test]
        public void Unsubscribe_ShouldRemoveEventListener()
        {
            // Arrange
            EventManager.Subscribe<TestEvent>(OnTestEvent);
            
            // Act
            EventManager.Unsubscribe<TestEvent>(OnTestEvent);

            // Assert
            Assert.AreEqual(0, EventManager.GetSubscriberCount<TestEvent>());
        }

        [Test]
        public void Publish_ShouldTriggerSubscribedEvent()
        {
            // Arrange
            EventManager.Subscribe<TestEvent>(OnTestEvent);

            // Act
            EventManager.Publish(new TestEvent("test message"));

            // Assert
            Assert.AreEqual(1, eventCount);
        }

        [Test]
        public void Publish_ShouldNotTriggerAfterUnsubscribe()
        {
            // Arrange
            EventManager.Subscribe<TestEvent>(OnTestEvent);
            EventManager.Unsubscribe<TestEvent>(OnTestEvent);

            // Act
            EventManager.Publish(new TestEvent("test message"));

            // Assert
            Assert.AreEqual(0, eventCount);
        }

        [Test]
        public void MultipleSubscribers_ShouldAllReceiveEvent()
        {
            // Arrange
            int count1 = 0, count2 = 0;
            EventManager.Subscribe<TestEvent>(e => count1++);
            EventManager.Subscribe<TestEvent>(e => count2++);

            // Act
            EventManager.Publish(new TestEvent("test message"));

            // Assert
            Assert.AreEqual(1, count1);
            Assert.AreEqual(1, count2);
        }

        [Test]
        public void ClearAllSubscriptions_ShouldRemoveAllListeners()
        {
            // Arrange
            EventManager.Subscribe<TestEvent>(OnTestEvent);
            EventManager.Subscribe<AnotherTestEvent>(OnAnotherTestEvent);

            // Act
            EventManager.ClearAllSubscriptions();

            // Assert
            Assert.AreEqual(0, EventManager.GetSubscriberCount<TestEvent>());
            Assert.AreEqual(0, EventManager.GetSubscriberCount<AnotherTestEvent>());
        }

        #endregion

        #region 辅助方法

        private void OnTestEvent(TestEvent eventData)
        {
            eventCount++;
        }

        private void OnAnotherTestEvent(AnotherTestEvent eventData)
        {
            eventCount++;
        }

        #endregion
    }

    #region 测试事件类

    public class TestEvent : EventBase
    {
        public string Message { get; }
        public TestEvent(string message) : base(null) 
        { 
            Message = message;
        }
    }

    public class AnotherTestEvent : EventBase
    {
        public string Message { get; }
        public AnotherTestEvent(string message) : base(null) 
        { 
            Message = message;
        }
    }

    #endregion
}
