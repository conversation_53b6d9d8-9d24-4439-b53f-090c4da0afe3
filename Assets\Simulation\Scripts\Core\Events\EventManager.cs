// 需求来源: 工作计划书.md 3.1 事件系统实现

using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace Simulation.Core.Events
{
    /// <summary>
    /// 基于发布/订阅模式的全局事件总线，支持异步事件处理
    /// 提供事件统计、过滤、监控等高级功能
    /// </summary>
    public static class EventManager
    {
        private static readonly Dictionary<Type, Delegate> s_eventDictionary = new Dictionary<Type, Delegate>();
        private static readonly Dictionary<Type, Delegate> s_asyncEventDictionary = new Dictionary<Type, Delegate>();
        private static readonly EventQueue s_eventQueue = new EventQueue();
        private static readonly object s_lockObject = new object();

        // 事件统计
        private static readonly Dictionary<Type, EventStatistics> s_eventStatistics = new Dictionary<Type, EventStatistics>();

        // 事件过滤器
        private static readonly List<IEventFilter> s_eventFilters = new List<IEventFilter>();

        // 调试和监控
        private static bool s_enableDebugLogging = false;
        private static bool s_enablePerformanceMonitoring = false;
        private static readonly List<EventBase> s_recentEvents = new List<EventBase>();
        private const int MAX_RECENT_EVENTS = 100;

        /// <summary>
        /// 订阅同步事件
        /// </summary>
        public static void Subscribe<T>(Action<T> listener) where T : EventBase
        {
            lock (s_lockObject)
            {
                var eventType = typeof(T);
                if (s_eventDictionary.TryGetValue(eventType, out var existingDelegate))
                {
                    s_eventDictionary[eventType] = Delegate.Combine(existingDelegate, listener);
                }
                else
                {
                    s_eventDictionary[eventType] = listener;
                }
            }
        }

        /// <summary>
        /// 订阅异步事件
        /// </summary>
        public static void SubscribeAsync<T>(Func<T, UniTask> listener) where T : EventBase
        {
            lock (s_lockObject)
            {
                var eventType = typeof(T);
                if (s_asyncEventDictionary.TryGetValue(eventType, out var existingDelegate))
                {
                    s_asyncEventDictionary[eventType] = Delegate.Combine(existingDelegate, listener);
                }
                else
                {
                    s_asyncEventDictionary[eventType] = listener;
                }
            }
        }

        /// <summary>
        /// 取消订阅同步事件
        /// </summary>
        public static void Unsubscribe<T>(Action<T> listener) where T : EventBase
        {
            lock (s_lockObject)
            {
                var eventType = typeof(T);
                if (s_eventDictionary.TryGetValue(eventType, out var existingDelegate))
                {
                    var result = Delegate.Remove(existingDelegate, listener);
                    if (result == null)
                    {
                        s_eventDictionary.Remove(eventType);
                    }
                    else
                    {
                        s_eventDictionary[eventType] = result;
                    }
                }
            }
        }

        /// <summary>
        /// 取消订阅异步事件
        /// </summary>
        public static void UnsubscribeAsync<T>(Func<T, UniTask> listener) where T : EventBase
        {
            lock (s_lockObject)
            {
                var eventType = typeof(T);
                if (s_asyncEventDictionary.TryGetValue(eventType, out var existingDelegate))
                {
                    var result = Delegate.Remove(existingDelegate, listener);
                    if (result == null)
                    {
                        s_asyncEventDictionary.Remove(eventType);
                    }
                    else
                    {
                        s_asyncEventDictionary[eventType] = result;
                    }
                }
            }
        }

        /// <summary>
        /// 立即发布事件（同步）
        /// </summary>
        public static void Publish<T>(T eventToPublish) where T : EventBase
        {
            // 检查事件过滤
            if (ShouldFilterEvent(eventToPublish))
            {
                DebugLog($"Event {typeof(T).Name} was filtered out");
                return;
            }

            // 更新统计信息
            UpdateEventStatistics(eventToPublish);
            RecordRecentEvent(eventToPublish);

            DebugLog($"Publishing event: {eventToPublish.GetDescription()}");

            var startTime = s_enablePerformanceMonitoring ? Time.realtimeSinceStartup : 0f;

            // 处理同步事件
            if (s_eventDictionary.TryGetValue(typeof(T), out var existingDelegate))
            {
                var action = existingDelegate as Action<T>;
                try
                {
                    action?.Invoke(eventToPublish);

                    if (s_enablePerformanceMonitoring)
                    {
                        UpdateProcessingStatistics(typeof(T), Time.realtimeSinceStartup - startTime);
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error in event handler for {typeof(T).Name}: {ex.Message}");
                    UpdateErrorStatistics(typeof(T));
                }
            }

            // 处理异步事件
            if (s_asyncEventDictionary.TryGetValue(typeof(T), out var asyncDelegate))
            {
                var asyncAction = asyncDelegate as Func<T, UniTask>;
                if (asyncAction != null)
                {
                    ProcessAsyncEventAsync(asyncAction, eventToPublish).Forget();
                }
            }
        }

        /// <summary>
        /// 将事件加入队列，延迟处理（支持优先级排序和递归保护）
        /// </summary>
        public static void PublishQueued<T>(T eventToPublish) where T : EventBase
        {
            // 检查事件过滤
            if (ShouldFilterEvent(eventToPublish))
            {
                DebugLog($"Queued event {typeof(T).Name} was filtered out");
                return;
            }

            // 更新统计信息
            UpdateEventStatistics(eventToPublish);
            RecordRecentEvent(eventToPublish);

            DebugLog($"Enqueueing event: {eventToPublish.GetDescription()}");

            // 加入队列（自动处理递归保护和优先级排序）
            bool success = s_eventQueue.Enqueue(eventToPublish);

            if (!success)
            {
                Debug.LogWarning($"[EventManager] Failed to enqueue event: {eventToPublish.EventType}");
            }
        }

        /// <summary>
        /// 处理队列中的所有事件（按优先级顺序，支持递归保护）
        /// </summary>
        public static void ProcessQueuedEvents()
        {
            if (s_eventQueue.IsEmpty) return;

            DebugLog($"Processing all queued events, queue size: {s_eventQueue.Count}");

            s_eventQueue.ProcessAll(ProcessQueuedEvent);
        }

        /// <summary>
        /// 处理队列中指定数量的事件（用于分帧处理，支持递归保护）
        /// </summary>
        public static void ProcessQueuedEvents(int maxEventsPerFrame)
        {
            if (s_eventQueue.IsEmpty || maxEventsPerFrame <= 0) return;

            DebugLog($"Processing {maxEventsPerFrame} queued events, queue size: {s_eventQueue.Count}");

            s_eventQueue.ProcessBatch(maxEventsPerFrame, ProcessQueuedEvent);
        }

        /// <summary>
        /// 处理单个队列事件的内部方法
        /// </summary>
        private static void ProcessQueuedEvent(EventBase eventToProcess)
        {
            var startTime = s_enablePerformanceMonitoring ? Time.realtimeSinceStartup : 0f;

            try
            {
                PublishEventByType(eventToProcess);

                if (s_enablePerformanceMonitoring)
                {
                    UpdateProcessingStatistics(eventToProcess.GetType(), Time.realtimeSinceStartup - startTime);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EventManager] Error processing queued event {eventToProcess.EventType}: {ex.Message}");
                UpdateErrorStatistics(eventToProcess.GetType());
            }
        }

        /// <summary>
        /// 异步发布事件并等待所有异步处理器完成
        /// </summary>
        public static async UniTask PublishAsync<T>(T eventToPublish, CancellationToken cancellationToken = default) where T : EventBase
        {
            // 处理同步事件
            Publish(eventToPublish);

            // 等待异步事件处理完成
            if (s_asyncEventDictionary.TryGetValue(typeof(T), out var asyncDelegate))
            {
                var asyncAction = asyncDelegate as Func<T, UniTask>;
                if (asyncAction != null)
                {
                    await ProcessAsyncEventAsync(asyncAction, eventToPublish, cancellationToken);
                }
            }
        }

        private static async UniTask ProcessAsyncEventAsync<T>(Func<T, UniTask> asyncAction, T eventToPublish, CancellationToken cancellationToken = default) where T : EventBase
        {
            try
            {
                await asyncAction(eventToPublish).AttachExternalCancellation(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 取消操作是正常的，不需要记录错误
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error in async event handler for {typeof(T).Name}: {ex.Message}");
            }
        }

        private static void PublishEventByType(EventBase eventToPublish)
        {
            var eventType = eventToPublish.GetType();

            if (s_eventDictionary.TryGetValue(eventType, out var existingDelegate))
            {
                try
                {
                    existingDelegate.DynamicInvoke(eventToPublish);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error in queued event handler for {eventType.Name}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 清理所有事件订阅
        /// </summary>
        public static void ClearAllSubscriptions()
        {
            lock (s_lockObject)
            {
                s_eventDictionary.Clear();
                s_asyncEventDictionary.Clear();
                s_eventQueue.Clear();
                s_eventStatistics.Clear();
                s_recentEvents.Clear();
            }
        }

        #region 事件统计和监控

        /// <summary>
        /// 启用或禁用调试日志
        /// </summary>
        public static void SetDebugLogging(bool enabled)
        {
            s_enableDebugLogging = enabled;
        }

        /// <summary>
        /// 启用或禁用性能监控
        /// </summary>
        public static void SetPerformanceMonitoring(bool enabled)
        {
            s_enablePerformanceMonitoring = enabled;
        }

        /// <summary>
        /// 获取事件类型的统计信息
        /// </summary>
        public static EventStatistics GetEventStatistics<T>() where T : EventBase
        {
            lock (s_lockObject)
            {
                var eventType = typeof(T);
                return s_eventStatistics.TryGetValue(eventType, out var stats) ? stats : new EventStatistics();
            }
        }

        /// <summary>
        /// 获取所有事件统计信息
        /// </summary>
        public static Dictionary<Type, EventStatistics> GetAllEventStatistics()
        {
            lock (s_lockObject)
            {
                return new Dictionary<Type, EventStatistics>(s_eventStatistics);
            }
        }

        /// <summary>
        /// 获取最近的事件列表
        /// </summary>
        public static List<EventBase> GetRecentEvents()
        {
            lock (s_lockObject)
            {
                return new List<EventBase>(s_recentEvents);
            }
        }

        /// <summary>
        /// 获取订阅者数量
        /// </summary>
        public static int GetSubscriberCount<T>() where T : EventBase
        {
            lock (s_lockObject)
            {
                var eventType = typeof(T);
                int count = 0;

                if (s_eventDictionary.TryGetValue(eventType, out var syncDelegate))
                {
                    count += syncDelegate.GetInvocationList().Length;
                }

                if (s_asyncEventDictionary.TryGetValue(eventType, out var asyncDelegate))
                {
                    count += asyncDelegate.GetInvocationList().Length;
                }

                return count;
            }
        }

        /// <summary>
        /// 获取队列中的事件数量
        /// </summary>
        public static int GetQueuedEventCount()
        {
            return s_eventQueue.Count;
        }

        /// <summary>
        /// 获取队列统计信息
        /// </summary>
        public static EventQueueStatistics GetQueueStatistics()
        {
            return s_eventQueue.Statistics;
        }

        /// <summary>
        /// 配置事件队列参数
        /// </summary>
        public static void ConfigureEventQueue(int maxSize = 1000, bool prioritySort = true, bool recursionProtection = true)
        {
            s_eventQueue.Configure(maxSize, prioritySort, recursionProtection);
        }

        /// <summary>
        /// 检查队列是否正在处理事件
        /// </summary>
        public static bool IsQueueProcessing()
        {
            return s_eventQueue.IsProcessing;
        }

        /// <summary>
        /// 重置队列统计信息
        /// </summary>
        public static void ResetQueueStatistics()
        {
            s_eventQueue.ResetStatistics();
        }

        #endregion

        #region 事件过滤

        /// <summary>
        /// 添加事件过滤器
        /// </summary>
        public static void AddEventFilter(IEventFilter filter)
        {
            lock (s_lockObject)
            {
                if (!s_eventFilters.Contains(filter))
                {
                    s_eventFilters.Add(filter);
                }
            }
        }

        /// <summary>
        /// 移除事件过滤器
        /// </summary>
        public static void RemoveEventFilter(IEventFilter filter)
        {
            lock (s_lockObject)
            {
                s_eventFilters.Remove(filter);
            }
        }

        /// <summary>
        /// 清除所有事件过滤器
        /// </summary>
        public static void ClearEventFilters()
        {
            lock (s_lockObject)
            {
                s_eventFilters.Clear();
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 检查事件是否应该被过滤
        /// </summary>
        private static bool ShouldFilterEvent(EventBase eventToPublish)
        {
            foreach (var filter in s_eventFilters)
            {
                if (!filter.ShouldProcess(eventToPublish))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 更新事件统计信息
        /// </summary>
        private static void UpdateEventStatistics(EventBase eventToPublish)
        {
            if (!s_enablePerformanceMonitoring) return;

            var eventType = eventToPublish.GetType();
            if (!s_eventStatistics.TryGetValue(eventType, out var stats))
            {
                stats = new EventStatistics();
                s_eventStatistics[eventType] = stats;
            }

            stats.TotalPublished++;
            stats.LastPublishedTime = eventToPublish.UnityTimestamp;
        }

        /// <summary>
        /// 记录最近的事件
        /// </summary>
        private static void RecordRecentEvent(EventBase eventToPublish)
        {
            s_recentEvents.Add(eventToPublish);
            if (s_recentEvents.Count > MAX_RECENT_EVENTS)
            {
                s_recentEvents.RemoveAt(0);
            }
        }

        /// <summary>
        /// 更新处理统计信息
        /// </summary>
        private static void UpdateProcessingStatistics(Type eventType, float processingTime)
        {
            if (s_eventStatistics.TryGetValue(eventType, out var stats))
            {
                stats.TotalProcessed++;
                // 计算平均处理时间
                stats.AverageProcessingTime = (stats.AverageProcessingTime * (stats.TotalProcessed - 1) + processingTime * 1000f) / stats.TotalProcessed;
            }
        }

        /// <summary>
        /// 更新错误统计信息
        /// </summary>
        private static void UpdateErrorStatistics(Type eventType)
        {
            if (s_eventStatistics.TryGetValue(eventType, out var stats))
            {
                stats.ErrorCount++;
            }
        }

        /// <summary>
        /// 调试日志
        /// </summary>
        private static void DebugLog(string message)
        {
            if (s_enableDebugLogging)
            {
                Debug.Log($"[EventManager] {message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 事件统计信息
    /// </summary>
    [Serializable]
    public class EventStatistics
    {
        public int TotalPublished;
        public int TotalProcessed;
        public float LastPublishedTime;
        public float AverageProcessingTime;
        public int ErrorCount;

        public override string ToString()
        {
            return $"Published: {TotalPublished}, Processed: {TotalProcessed}, Errors: {ErrorCount}, Avg Time: {AverageProcessingTime:F3}ms";
        }
    }

    /// <summary>
    /// 事件过滤器接口
    /// </summary>
    public interface IEventFilter
    {
        /// <summary>
        /// 判断事件是否应该被处理
        /// </summary>
        bool ShouldProcess(EventBase eventToPublish);
    }
}