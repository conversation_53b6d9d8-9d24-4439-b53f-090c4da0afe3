// 需求来源: 工作计划书.md 3.1 事件系统实现
// 高级事件队列管理器，提供多队列管理、负载均衡、优先级调度等功能

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace Simulation.Core.Events
{
    /// <summary>
    /// 高级事件队列管理器
    /// 支持多队列、负载均衡、优先级调度、异步处理等功能
    /// </summary>
    public class EventQueueManager : MonoBehaviour
    {
        [Header("队列配置")]
        [SerializeField] private int maxQueueSize = 1000;
        [SerializeField] private bool enablePrioritySort = true;
        [SerializeField] private bool enableRecursionProtection = true;
        [SerializeField] private int maxEventsPerFrame = 10;

        [Header("多队列配置")]
        [SerializeField] private bool enableMultipleQueues = false;
        [SerializeField] private int numberOfQueues = 3;
        [SerializeField] private QueueSchedulingStrategy schedulingStrategy = QueueSchedulingStrategy.RoundRobin;

        [Header("性能监控")]
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private float performanceReportInterval = 5f;

        [Header("负载均衡")]
        [SerializeField] private bool enableLoadBalancing = false;
        [SerializeField] private float loadBalanceThreshold = 0.8f;

        // 多队列系统
        private Dictionary<int, EventQueue> queues = new Dictionary<int, EventQueue>();
        private int currentQueueIndex = 0;
        private float lastPerformanceReport = 0f;

        // 队列调度策略
        public enum QueueSchedulingStrategy
        {
            RoundRobin,     // 轮询
            LoadBased,      // 基于负载
            PriorityBased,  // 基于优先级
            Random          // 随机
        }

        private void Awake()
        {
            InitializeQueues();
        }

        private void Start()
        {
            ConfigureEventManager();
        }

        private void Update()
        {
            ProcessQueues();
            ReportPerformance();
        }

        /// <summary>
        /// 初始化队列系统
        /// </summary>
        private void InitializeQueues()
        {
            if (enableMultipleQueues)
            {
                for (int i = 0; i < numberOfQueues; i++)
                {
                    var queue = new EventQueue();
                    queue.Configure(maxQueueSize, enablePrioritySort, enableRecursionProtection);
                    queues[i] = queue;
                }
                Debug.Log($"[EventQueueManager] 初始化了 {numberOfQueues} 个事件队列");
            }
            else
            {
                // 使用EventManager的默认队列
                EventManager.ConfigureEventQueue(maxQueueSize, enablePrioritySort, enableRecursionProtection);
            }
        }

        /// <summary>
        /// 配置EventManager
        /// </summary>
        private void ConfigureEventManager()
        {
            EventManager.SetPerformanceMonitoring(enablePerformanceMonitoring);
            EventManager.SetDebugLogging(Application.isEditor);
        }

        /// <summary>
        /// 处理所有队列
        /// </summary>
        private void ProcessQueues()
        {
            if (enableMultipleQueues)
            {
                ProcessMultipleQueues();
            }
            else
            {
                // 使用EventManager的默认队列处理
                EventManager.ProcessQueuedEvents(maxEventsPerFrame);
            }
        }

        /// <summary>
        /// 处理多队列系统
        /// </summary>
        private void ProcessMultipleQueues()
        {
            if (queues.Count == 0) return;

            int eventsPerQueue = Mathf.Max(1, maxEventsPerFrame / queues.Count);
            
            switch (schedulingStrategy)
            {
                case QueueSchedulingStrategy.RoundRobin:
                    ProcessQueuesRoundRobin(eventsPerQueue);
                    break;
                    
                case QueueSchedulingStrategy.LoadBased:
                    ProcessQueuesLoadBased(eventsPerQueue);
                    break;
                    
                case QueueSchedulingStrategy.PriorityBased:
                    ProcessQueuesPriorityBased(eventsPerQueue);
                    break;
                    
                case QueueSchedulingStrategy.Random:
                    ProcessQueuesRandom(eventsPerQueue);
                    break;
            }
        }

        /// <summary>
        /// 轮询处理队列
        /// </summary>
        private void ProcessQueuesRoundRobin(int eventsPerQueue)
        {
            foreach (var kvp in queues)
            {
                if (!kvp.Value.IsEmpty)
                {
                    kvp.Value.ProcessBatch(eventsPerQueue, ProcessQueueEvent);
                }
            }
        }

        /// <summary>
        /// 基于负载处理队列
        /// </summary>
        private void ProcessQueuesLoadBased(int eventsPerQueue)
        {
            // 按队列大小排序，优先处理负载高的队列
            var sortedQueues = queues.OrderByDescending(kvp => kvp.Value.Count);
            
            foreach (var kvp in sortedQueues)
            {
                if (!kvp.Value.IsEmpty)
                {
                    int adjustedEvents = enableLoadBalancing ? 
                        AdjustEventsForLoad(kvp.Value, eventsPerQueue) : eventsPerQueue;
                    kvp.Value.ProcessBatch(adjustedEvents, ProcessQueueEvent);
                }
            }
        }

        /// <summary>
        /// 基于优先级处理队列
        /// </summary>
        private void ProcessQueuesPriorityBased(int eventsPerQueue)
        {
            // 获取所有队列中的最高优先级事件
            var allEvents = new List<(int queueId, EventBase eventItem)>();
            
            foreach (var kvp in queues)
            {
                var events = kvp.Value.GetEvents();
                foreach (var eventItem in events.Take(eventsPerQueue))
                {
                    allEvents.Add((kvp.Key, eventItem));
                }
            }

            // 按优先级排序
            allEvents.Sort((a, b) => a.eventItem.CompareTo(b.eventItem));

            // 处理最高优先级的事件
            int processedCount = 0;
            foreach (var (queueId, eventItem) in allEvents.Take(maxEventsPerFrame))
            {
                queues[queueId].ProcessBatch(1, ProcessQueueEvent);
                processedCount++;
            }
        }

        /// <summary>
        /// 随机处理队列
        /// </summary>
        private void ProcessQueuesRandom(int eventsPerQueue)
        {
            var queueIds = queues.Keys.ToList();
            
            for (int i = 0; i < maxEventsPerFrame && queueIds.Count > 0; i++)
            {
                int randomIndex = UnityEngine.Random.Range(0, queueIds.Count);
                int queueId = queueIds[randomIndex];
                
                if (!queues[queueId].IsEmpty)
                {
                    queues[queueId].ProcessBatch(1, ProcessQueueEvent);
                }
                else
                {
                    queueIds.RemoveAt(randomIndex);
                }
            }
        }

        /// <summary>
        /// 根据负载调整处理事件数量
        /// </summary>
        private int AdjustEventsForLoad(EventQueue queue, int baseEvents)
        {
            var stats = queue.Statistics;
            float loadRatio = (float)stats.CurrentQueueSize / maxQueueSize;
            
            if (loadRatio > loadBalanceThreshold)
            {
                // 高负载时增加处理数量
                return Mathf.RoundToInt(baseEvents * (1f + loadRatio));
            }
            
            return baseEvents;
        }

        /// <summary>
        /// 处理队列事件
        /// </summary>
        private void ProcessQueueEvent(EventBase eventToProcess)
        {
            try
            {
                // 通过EventManager发布事件
                var eventType = eventToProcess.GetType();
                var publishMethod = typeof(EventManager).GetMethod("Publish").MakeGenericMethod(eventType);
                publishMethod.Invoke(null, new object[] { eventToProcess });
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EventQueueManager] 处理队列事件时发生错误: {eventToProcess.EventType}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 性能报告
        /// </summary>
        private void ReportPerformance()
        {
            if (!enablePerformanceMonitoring) return;
            
            if (Time.time - lastPerformanceReport >= performanceReportInterval)
            {
                lastPerformanceReport = Time.time;
                
                if (enableMultipleQueues)
                {
                    ReportMultipleQueuesPerformance();
                }
                else
                {
                    ReportSingleQueuePerformance();
                }
            }
        }

        /// <summary>
        /// 报告多队列性能
        /// </summary>
        private void ReportMultipleQueuesPerformance()
        {
            Debug.Log("[EventQueueManager] 多队列性能报告:");
            
            foreach (var kvp in queues)
            {
                var stats = kvp.Value.Statistics;
                Debug.Log($"  队列 {kvp.Key}: {stats}");
            }
        }

        /// <summary>
        /// 报告单队列性能
        /// </summary>
        private void ReportSingleQueuePerformance()
        {
            var queueStats = EventManager.GetQueueStatistics();
            var eventStats = EventManager.GetAllEventStatistics();
            
            Debug.Log($"[EventQueueManager] 队列统计: {queueStats}");
            Debug.Log($"[EventQueueManager] 事件类型数量: {eventStats.Count}");
        }

        /// <summary>
        /// 将事件分配到指定队列
        /// </summary>
        public void EnqueueToQueue(int queueId, EventBase eventToEnqueue)
        {
            if (enableMultipleQueues && queues.ContainsKey(queueId))
            {
                queues[queueId].Enqueue(eventToEnqueue);
            }
            else
            {
                EventManager.PublishQueued(eventToEnqueue);
            }
        }

        /// <summary>
        /// 获取最佳队列ID（负载均衡）
        /// </summary>
        public int GetBestQueueId()
        {
            if (!enableMultipleQueues) return 0;
            
            // 选择负载最小的队列
            return queues.OrderBy(kvp => kvp.Value.Count).First().Key;
        }

        /// <summary>
        /// 获取所有队列统计信息
        /// </summary>
        public Dictionary<int, EventQueueStatistics> GetAllQueueStatistics()
        {
            var result = new Dictionary<int, EventQueueStatistics>();
            
            if (enableMultipleQueues)
            {
                foreach (var kvp in queues)
                {
                    result[kvp.Key] = kvp.Value.Statistics;
                }
            }
            else
            {
                result[0] = EventManager.GetQueueStatistics();
            }
            
            return result;
        }

        private void OnDestroy()
        {
            // 清理所有队列
            foreach (var queue in queues.Values)
            {
                queue.Clear();
            }
            queues.Clear();
        }
    }
}
