"unity-ilpp-2b81e9903e7db2c7bbb81cb4696189d7" p "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll" "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed" "UNITY_6000_1_7" "UNITY_6000_1" "UNITY_6000" "UNITY_5_3_OR_NEWER" "UNITY_5_4_OR_NEWER" "UNITY_5_5_OR_NEWER" "UNITY_5_6_OR_NEWER" "UNITY_2017_1_OR_NEWER" "UNITY_2017_2_OR_NEWER" "UNITY_2017_3_OR_NEWER" "UNITY_2017_4_OR_NEWER" "UNITY_2018_1_OR_NEWER" "UNITY_2018_2_OR_NEWER" "UNITY_2018_3_OR_NEWER" "UNITY_2018_4_OR_NEWER" "UNITY_2019_1_OR_NEWER" "UNITY_2019_2_OR_NEWER" "UNITY_2019_3_OR_NEWER" "UNITY_2019_4_OR_NEWER" "UNITY_2020_1_OR_NEWER" "UNITY_2020_2_OR_NEWER" "UNITY_2020_3_OR_NEWER" "UNITY_2021_1_OR_NEWER" "UNITY_2021_2_OR_NEWER" "UNITY_2021_3_OR_NEWER" "UNITY_2022_1_OR_NEWER" "UNITY_2022_2_OR_NEWER" "UNITY_2022_3_OR_NEWER" "UNITY_2023_1_OR_NEWER" "UNITY_2023_2_OR_NEWER" "UNITY_2023_3_OR_NEWER" "UNITY_6000_0_OR_NEWER" "UNITY_6000_1_OR_NEWER" "PLATFORM_ARCH_64" "UNITY_64" "UNITY_INCLUDE_TESTS" "ENABLE_AR" "ENABLE_AUDIO" "ENABLE_CACHING" "ENABLE_CLOTH" "ENABLE_EVENT_QUEUE" "ENABLE_MICROPHONE" "ENABLE_MULTIPLE_DISPLAYS" "ENABLE_PHYSICS" "ENABLE_TEXTURE_STREAMING" "ENABLE_VIRTUALTEXTURING" "ENABLE_LZMA" "ENABLE_UNITYEVENTS" "ENABLE_VR" "ENABLE_WEBCAM" "ENABLE_UNITYWEBREQUEST" "ENABLE_WWW" "ENABLE_CLOUD_SERVICES" "ENABLE_CLOUD_SERVICES_ADS" "ENABLE_CLOUD_SERVICES_USE_WEBREQUEST" "ENABLE_CLOUD_SERVICES_CRASH_REPORTING" "ENABLE_CLOUD_SERVICES_PURCHASING" "ENABLE_CLOUD_SERVICES_ANALYTICS" "ENABLE_CLOUD_SERVICES_BUILD" "ENABLE_EDITOR_GAME_SERVICES" "ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT" "ENABLE_CLOUD_LICENSE" "ENABLE_EDITOR_HUB_LICENSE" "ENABLE_WEBSOCKET_CLIENT" "ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API" "ENABLE_DIRECTOR_AUDIO" "ENABLE_DIRECTOR_TEXTURE" "ENABLE_MANAGED_JOBS" "ENABLE_MANAGED_TRANSFORM_JOBS" "ENABLE_MANAGED_ANIMATION_JOBS" "ENABLE_MANAGED_AUDIO_JOBS" "ENABLE_MANAGED_UNITYTLS" "INCLUDE_DYNAMIC_GI" "ENABLE_SCRIPTING_GC_WBARRIERS" "PLATFORM_SUPPORTS_MONO" "RENDER_SOFTWARE_CURSOR" "ENABLE_MARSHALLING_TESTS" "ENABLE_VIDEO" "ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK" "ENABLE_ACCELERATOR_CLIENT_DEBUGGING" "TEXTCORE_1_0_OR_NEWER" "EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED" "PLATFORM_STANDALONE_WIN" "PLATFORM_STANDALONE" "UNITY_STANDALONE_WIN" "UNITY_STANDALONE" "ENABLE_RUNTIME_GI" "ENABLE_MOVIES" "ENABLE_NETWORK" "ENABLE_NVIDIA" "ENABLE_AMD" "ENABLE_CRUNCH_TEXTURE_COMPRESSION" "ENABLE_OUT_OF_PROCESS_CRASH_HANDLER" "ENABLE_CLUSTER_SYNC" "ENABLE_CLUSTERINPUT" "PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP" "GFXDEVICE_WAITFOREVENT_MESSAGEPUMP" "PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER" "PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION" "PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS" "ENABLE_MONO" "NET_4_6" "NET_UNITY_4_8" "ENABLE_PROFILER" "DEBUG" "TRACE" "UNITY_ASSERTIONS" "UNITY_EDITOR" "UNITY_EDITOR_64" "UNITY_EDITOR_WIN" "ENABLE_UNITY_COLLECTIONS_CHECKS" "ENABLE_BURST_AOT" "UNITY_TEAM_LICENSE" "ENABLE_CUSTOM_RENDER_TEXTURE" "ENABLE_DIRECTOR" "ENABLE_LOCALIZATION" "ENABLE_SPRITES" "ENABLE_TERRAIN" "ENABLE_TILEMAP" "ENABLE_TIMELINE" "ENABLE_INPUT_SYSTEM" "ENABLE_LEGACY_INPUT_MANAGER" "TEXTCORE_FONT_ENGINE_1_5_OR_NEWER" "TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER" "DOTWEEN" "UNITASK_DOTWEEN_SUPPORT" "ENVIRO_3" "ENVIRO_URP" "CSHARP_7_OR_LATER" "CSHARP_7_3_OR_NEWER" "UNITY_EDITOR_ONLY_COMPILATION" -r "Library\Bee\artifacts\1900b0aEDbg.dag\Assembly-CSharp.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Autodesk.Fbx.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Autodesk.Fbx.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.LoadImageAsync.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTEditor.Demo.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTEditor.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTEditor.URP.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTEditor.URP.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTEditor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTExtensions.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTExtensions.URP.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTExtensions.URP.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTExtensions.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTImporter.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTScripting.Common.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTScripting.Jint.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTScripting.Jint.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTScripting.Roslyn.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.RTScripting.Roslyn.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.Storage.Addressables.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.Storage.Core.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.Storage.Core.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.Storage.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.Storage.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.Storage.ShaderUtil.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Battlehub.Storage.ShaderUtil.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\CodeAnalysis.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\DOTween.Modules.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Draco.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Draco.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Enviro3.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Enviro3.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\HSVPicker.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\IngameDebugConsole.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\IngameDebugConsole.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Ktx.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\PPv2URPConverters.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\PsdPlugin.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Simulation.Tests.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Simulation.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\TriLib.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\TriLib.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UIShapesKit.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UIShapesKit.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UMP.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UniTask.Addressables.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UniTask.DOTween.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UniTask.Linq.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UniTask.TextMeshPro.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UniTask.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.2D.Common.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.2D.Common.Path.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.2D.Common.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.2D.Psdimporter.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.2D.Sprite.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.2D.Tilemap.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.AI.Navigation.Editor.ConversionSystem.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.AI.Navigation.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.AI.Navigation.Updater.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.AI.Navigation.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Addressables.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Addressables.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Burst.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Burst.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Collections.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Collections.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Formats.Fbx.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Formats.Fbx.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.InputSystem.ForUI.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.InputSystem.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.InternalAPIEditorBridge.001.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.InternalAPIEngineBridge.001.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Mathematics.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Mathematics.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Multiplayer.Center.Common.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Multiplayer.Center.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.PlasticSCM.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ProBuilder.AddOns.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ProBuilder.AssetIdRemapUtility.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ProBuilder.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ProBuilder.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Profiling.Core.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipeline.Universal.ShaderLibrary.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Core.Editor.Shared.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Core.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Core.Runtime.Shared.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Core.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Core.ShaderLibrary.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.GPUDriven.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Universal.2D.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Universal.Config.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Universal.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Universal.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.RenderPipelines.Universal.Shaders.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Rendering.LightTransport.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Rendering.LightTransport.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ResourceManager.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Rider.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ScriptableBuildPipeline.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ScriptableBuildPipeline.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Searcher.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Settings.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.ShaderGraph.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.SharpZipLib.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.SharpZipLib.Utils.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.TextMeshPro.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.TextMeshPro.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Timeline.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.Timeline.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.VisualEffectGraph.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.VisualEffectGraph.Runtime.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\Unity.VisualStudio.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UnityEditor.TestRunner.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UnityEditor.UI.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UnityEngine.TestRunner.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\UnityEngine.UI.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\com.Tivadar.Best.HTTP.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\com.Tivadar.Best.MQTT.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\com.Tivadar.Best.TLSSecurity.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\com.Tivadar.Best.WebSockets.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\glTFast.Animation.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\glTFast.Editor.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\glTFast.dll" "Library\Bee\artifacts\1900b0aEDbg.dag\glTFast.dots.dll" "Assets\Battlehub\Protobuf.Net\protobuf-net.dll" "Assets\Battlehub\RTEditor\ThirdParty\ICSharpCode.SharpZipLib.dll" "Assets\Battlehub\RTEditor\ThirdParty\UnityWeld\UnityWeld.dll" "Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Acornima.dll" "Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Jint.dll" "Assets\Battlehub\StorageData\Generated\StorageTypeModel.dll" "Assets\Plugins\DOTween\DOTween.dll" "Assets\Plugins\DOTween\Editor\DOTweenEditor.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Dae.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Fbx.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Gltf.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Gltf.Draco.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.HDRLoader.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Obj.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Ply.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Stl.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Textures.dll" "Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.ThreeMf.dll" "Assets\TriLib\TriLibCore\Plugins\Dependencies\IxMilia.ThreeMf.dll" "Assets\TriLib\TriLibCore\Plugins\Dependencies\LibTessDotNet.dll" "Assets\TriLib\TriLibCore\Plugins\Dependencies\SafeStbImageSharp.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEditor.Graphs.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AMDModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll" "E:\Unity\6000.1.7f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll" "E:\Unity\6000.1.7f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll" "Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll" "Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll" "Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll" "Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll" "Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll" "Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll" "Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll" "Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll" "Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll" "Library\PackageCache\com.unity.sharp-zip-lib@6b61f82b0cb3\Runtime\Unity.SharpZipLib.dll"