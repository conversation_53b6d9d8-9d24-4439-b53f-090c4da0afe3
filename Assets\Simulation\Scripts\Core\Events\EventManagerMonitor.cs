// 需求来源: 工作计划书.md 3.1 事件系统实现
// EventManager的监控和管理工具

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Simulation.Core.Events
{
    /// <summary>
    /// EventManager监控组件
    /// 提供运行时监控、调试和管理功能
    /// </summary>
    public class EventManagerMonitor : MonoBehaviour
    {
        [Header("监控配置")]
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private bool showGUI = true;
        [SerializeField] private int maxEventsPerFrame = 10;

        [Header("过滤器配置")]
        [SerializeField] private bool enableRateLimit = false;
        [SerializeField] private float rateLimitInterval = 0.1f;
        [SerializeField] private bool enableDebugModeFilter = true;

        [Header("统计显示")]
        [SerializeField] private bool showEventStatistics = true;
        [SerializeField] private bool showRecentEvents = true;
        [SerializeField] private int maxRecentEventsDisplay = 10;

        private Vector2 scrollPosition;
        private bool showStatistics = true;
        private bool showFilters = false;
        private bool showRecentEventsPanel = false;

        private void Start()
        {
            InitializeEventManager();
            SetupEventFilters();
        }

        private void Update()
        {
            // 分帧处理队列中的事件
            if (maxEventsPerFrame > 0)
            {
                EventManager.ProcessQueuedEvents(maxEventsPerFrame);
            }
            else
            {
                EventManager.ProcessQueuedEvents();
            }
        }

        private void OnDestroy()
        {
            // 清理资源
            EventManager.ClearEventFilters();
        }

        /// <summary>
        /// 初始化EventManager设置
        /// </summary>
        private void InitializeEventManager()
        {
            EventManager.SetDebugLogging(enableDebugLogging);
            EventManager.SetPerformanceMonitoring(enablePerformanceMonitoring);
        }

        /// <summary>
        /// 设置事件过滤器
        /// </summary>
        private void SetupEventFilters()
        {
            EventManager.ClearEventFilters();

            if (enableRateLimit)
            {
                EventManager.AddEventFilter(new RateLimitFilter(rateLimitInterval));
            }

            if (enableDebugModeFilter)
            {
                EventManager.AddEventFilter(new DebugModeFilter(false));
            }
        }

        #region GUI显示

        private void OnGUI()
        {
            if (!showGUI) return;

            GUILayout.BeginArea(new Rect(10, 10, 400, Screen.height - 20));
            GUILayout.BeginVertical("box");

            GUILayout.Label("Event Manager Monitor", GUI.skin.label);

            // 控制按钮
            GUILayout.BeginHorizontal();
            if (GUILayout.Button(showStatistics ? "隐藏统计" : "显示统计"))
            {
                showStatistics = !showStatistics;
            }
            if (GUILayout.Button(showFilters ? "隐藏过滤器" : "显示过滤器"))
            {
                showFilters = !showFilters;
            }
            if (GUILayout.Button(showRecentEventsPanel ? "隐藏事件" : "显示事件"))
            {
                showRecentEventsPanel = !showRecentEventsPanel;
            }
            GUILayout.EndHorizontal();

            scrollPosition = GUILayout.BeginScrollView(scrollPosition);

            // 显示基本信息
            ShowBasicInfo();

            // 显示统计信息
            if (showStatistics && showEventStatistics)
            {
                ShowEventStatistics();
            }

            // 显示过滤器信息
            if (showFilters)
            {
                ShowFilterInfo();
            }

            // 显示最近事件
            if (showRecentEventsPanel && showRecentEvents)
            {
                ShowRecentEvents();
            }

            GUILayout.EndScrollView();
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        private void ShowBasicInfo()
        {
            GUILayout.Label("基本信息", GUI.skin.label);
            GUILayout.Label($"队列事件数: {EventManager.GetQueuedEventCount()}");
            GUILayout.Label($"调试日志: {(enableDebugLogging ? "开启" : "关闭")}");
            GUILayout.Label($"性能监控: {(enablePerformanceMonitoring ? "开启" : "关闭")}");
            GUILayout.Space(10);
        }

        private void ShowEventStatistics()
        {
            GUILayout.Label("事件统计", GUI.skin.label);
            
            var allStats = EventManager.GetAllEventStatistics();
            if (allStats.Count == 0)
            {
                GUILayout.Label("暂无事件统计数据");
                return;
            }

            foreach (var kvp in allStats.OrderByDescending(x => x.Value.TotalPublished))
            {
                var eventType = kvp.Key;
                var stats = kvp.Value;
                
                GUILayout.BeginVertical("box");
                GUILayout.Label($"{eventType.Name}", GUI.skin.label);
                GUILayout.Label($"  发布: {stats.TotalPublished}");
                GUILayout.Label($"  处理: {stats.TotalProcessed}");
                GUILayout.Label($"  错误: {stats.ErrorCount}");
                GUILayout.Label($"  平均耗时: {stats.AverageProcessingTime:F2}ms");
                GUILayout.EndVertical();
            }
            GUILayout.Space(10);
        }

        private void ShowFilterInfo()
        {
            GUILayout.Label("过滤器设置", GUI.skin.label);

            bool newRateLimit = GUILayout.Toggle(enableRateLimit, "频率限制");
            if (newRateLimit != enableRateLimit)
            {
                enableRateLimit = newRateLimit;
                SetupEventFilters();
            }

            if (enableRateLimit)
            {
                GUILayout.BeginHorizontal();
                GUILayout.Label("限制间隔(秒):");
                string intervalStr = GUILayout.TextField(rateLimitInterval.ToString("F2"));
                if (float.TryParse(intervalStr, out float newInterval) && Math.Abs(newInterval - rateLimitInterval) > 0.001f)
                {
                    rateLimitInterval = newInterval;
                    SetupEventFilters();
                }
                GUILayout.EndHorizontal();
            }

            bool newDebugFilter = GUILayout.Toggle(enableDebugModeFilter, "调试模式过滤");
            if (newDebugFilter != enableDebugModeFilter)
            {
                enableDebugModeFilter = newDebugFilter;
                SetupEventFilters();
            }

            GUILayout.Space(10);
        }

        private void ShowRecentEvents()
        {
            GUILayout.Label("最近事件", GUI.skin.label);
            
            var recentEvents = EventManager.GetRecentEvents();
            if (recentEvents.Count == 0)
            {
                GUILayout.Label("暂无最近事件");
                return;
            }

            int displayCount = Mathf.Min(maxRecentEventsDisplay, recentEvents.Count);
            for (int i = recentEvents.Count - displayCount; i < recentEvents.Count; i++)
            {
                var eventItem = recentEvents[i];
                GUILayout.BeginVertical("box");
                GUILayout.Label($"{eventItem.EventType}", GUI.skin.label);
                GUILayout.Label($"时间: {eventItem.UnityTimestamp:F2}s");
                GUILayout.Label($"发送者: {eventItem.SenderId}");
                GUILayout.Label($"优先级: {eventItem.Priority}");
                GUILayout.EndVertical();
            }
        }

        #endregion

        #region 公共接口

        /// <summary>
        /// 清除所有统计数据
        /// </summary>
        [ContextMenu("清除统计数据")]
        public void ClearStatistics()
        {
            EventManager.ClearAllSubscriptions();
            Debug.Log("事件统计数据已清除");
        }

        /// <summary>
        /// 发布测试事件
        /// </summary>
        [ContextMenu("发布测试事件")]
        public void PublishTestEvent()
        {
            EventManager.Publish(new DebugEvent(this, "测试事件", "Monitor"));
        }

        /// <summary>
        /// 切换调试日志
        /// </summary>
        public void ToggleDebugLogging()
        {
            enableDebugLogging = !enableDebugLogging;
            EventManager.SetDebugLogging(enableDebugLogging);
        }

        /// <summary>
        /// 切换性能监控
        /// </summary>
        public void TogglePerformanceMonitoring()
        {
            enablePerformanceMonitoring = !enablePerformanceMonitoring;
            EventManager.SetPerformanceMonitoring(enablePerformanceMonitoring);
        }

        #endregion
    }
}
