{"format": 1, "restore": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {}}, "projects": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj", "projectName": "Battlehub.Storage.Core.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Core.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj", "projectName": "Battlehub.Storage.Core.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Core.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj", "projectName": "Battlehub.Storage.ShaderUtil.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.ShaderUtil.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}