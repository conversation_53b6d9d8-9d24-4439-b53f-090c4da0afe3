// 需求来源: 工作计划书.md 3.1 事件系统实现
// EventQueue类的单元测试

using System;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;
using Simulation.Core.Events;

namespace Simulation.Tests.Core
{
    /// <summary>
    /// EventQueue类的单元测试
    /// </summary>
    [TestFixture]
    public class EventQueueTests
    {
        private EventQueue eventQueue;
        private GameObject testGameObject;
        private List<EventBase> processedEvents;

        [SetUp]
        public void SetUp()
        {
            eventQueue = new EventQueue();
            testGameObject = new GameObject("TestObject");
            processedEvents = new List<EventBase>();
        }

        [TearDown]
        public void TearDown()
        {
            eventQueue?.Clear();
            if (testGameObject != null)
            {
                UnityEngine.Object.DestroyImmediate(testGameObject);
            }
            processedEvents?.Clear();
        }

        /// <summary>
        /// 测试基本的入队和出队操作
        /// </summary>
        [Test]
        public void TestBasicEnqueueDequeue()
        {
            // Arrange
            var debugEvent = new DebugEvent(testGameObject, "测试消息");

            // Act
            bool enqueueResult = eventQueue.Enqueue(debugEvent);
            
            // Assert
            Assert.IsTrue(enqueueResult, "入队操作应该成功");
            Assert.AreEqual(1, eventQueue.Count, "队列中应该有1个事件");
            Assert.IsFalse(eventQueue.IsEmpty, "队列不应该为空");

            // Process events
            eventQueue.ProcessAll(e => processedEvents.Add(e));
            
            Assert.AreEqual(1, processedEvents.Count, "应该处理1个事件");
            Assert.AreEqual(debugEvent, processedEvents[0], "处理的事件应该是入队的事件");
            Assert.IsTrue(eventQueue.IsEmpty, "处理后队列应该为空");
        }

        /// <summary>
        /// 测试优先级排序
        /// </summary>
        [Test]
        public void TestPriorityOrdering()
        {
            // Arrange
            eventQueue.Configure(maxSize: 100, prioritySort: true, recursionProtection: false);
            
            var lowPriorityEvent = new DebugEvent(testGameObject, "低优先级");
            var mediumPriorityEvent = new EntityStateChangedEvent(testGameObject, Guid.NewGuid(), "A", "B");
            var highPriorityEvent = new SimulationStartedEvent(testGameObject, "高优先级");

            // Act - 以相反的优先级顺序入队
            eventQueue.Enqueue(lowPriorityEvent);
            eventQueue.Enqueue(mediumPriorityEvent);
            eventQueue.Enqueue(highPriorityEvent);

            // Process events
            eventQueue.ProcessAll(e => processedEvents.Add(e));

            // Assert
            Assert.AreEqual(3, processedEvents.Count, "应该处理3个事件");
            Assert.AreEqual(highPriorityEvent, processedEvents[0], "第一个应该是高优先级事件");
            Assert.AreEqual(mediumPriorityEvent, processedEvents[1], "第二个应该是中等优先级事件");
            Assert.AreEqual(lowPriorityEvent, processedEvents[2], "第三个应该是低优先级事件");
        }

        /// <summary>
        /// 测试队列大小限制
        /// </summary>
        [Test]
        public void TestQueueSizeLimit()
        {
            // Arrange
            eventQueue.Configure(maxSize: 3, prioritySort: false, recursionProtection: false);

            // Act - 尝试入队超过限制的事件
            bool result1 = eventQueue.Enqueue(new DebugEvent(testGameObject, "事件1"));
            bool result2 = eventQueue.Enqueue(new DebugEvent(testGameObject, "事件2"));
            bool result3 = eventQueue.Enqueue(new DebugEvent(testGameObject, "事件3"));
            bool result4 = eventQueue.Enqueue(new DebugEvent(testGameObject, "事件4")); // 应该失败

            // Assert
            Assert.IsTrue(result1, "前3个事件应该成功入队");
            Assert.IsTrue(result2, "前3个事件应该成功入队");
            Assert.IsTrue(result3, "前3个事件应该成功入队");
            Assert.IsFalse(result4, "第4个事件应该入队失败");
            Assert.AreEqual(3, eventQueue.Count, "队列中应该只有3个事件");

            var stats = eventQueue.Statistics;
            Assert.AreEqual(4, stats.TotalEnqueued, "应该记录4次入队尝试");
            Assert.AreEqual(1, stats.TotalDropped, "应该记录1次丢弃");
        }

        /// <summary>
        /// 测试递归调用保护
        /// </summary>
        [Test]
        public void TestRecursionProtection()
        {
            // Arrange
            eventQueue.Configure(maxSize: 100, prioritySort: false, recursionProtection: true);
            int recursiveCallCount = 0;

            // Act
            eventQueue.Enqueue(new DebugEvent(testGameObject, "初始事件"));
            
            eventQueue.ProcessAll(e =>
            {
                processedEvents.Add(e);
                recursiveCallCount++;
                
                if (recursiveCallCount < 3)
                {
                    // 在处理过程中尝试入队新事件（递归调用）
                    eventQueue.Enqueue(new DebugEvent(testGameObject, $"递归事件{recursiveCallCount}"));
                }
            });

            // Assert
            Assert.AreEqual(1, processedEvents.Count, "第一次处理应该只处理1个事件");
            Assert.AreEqual(2, eventQueue.Count, "递归事件应该在缓冲区中");

            // 再次处理
            eventQueue.ProcessAll(e => processedEvents.Add(e));
            
            Assert.AreEqual(3, processedEvents.Count, "第二次处理应该处理递归事件");
        }

        /// <summary>
        /// 测试分批处理
        /// </summary>
        [Test]
        public void TestBatchProcessing()
        {
            // Arrange
            for (int i = 0; i < 5; i++)
            {
                eventQueue.Enqueue(new DebugEvent(testGameObject, $"事件{i}"));
            }

            // Act - 分两批处理
            eventQueue.ProcessBatch(3, e => processedEvents.Add(e));
            Assert.AreEqual(3, processedEvents.Count, "第一批应该处理3个事件");
            Assert.AreEqual(2, eventQueue.Count, "队列中应该还有2个事件");

            eventQueue.ProcessBatch(3, e => processedEvents.Add(e));
            Assert.AreEqual(5, processedEvents.Count, "第二批应该处理剩余的2个事件");
            Assert.IsTrue(eventQueue.IsEmpty, "处理完成后队列应该为空");
        }

        /// <summary>
        /// 测试统计信息
        /// </summary>
        [Test]
        public void TestStatistics()
        {
            // Arrange & Act
            eventQueue.Enqueue(new DebugEvent(testGameObject, "事件1"));
            eventQueue.Enqueue(new DebugEvent(testGameObject, "事件2"));
            
            var statsBefore = eventQueue.Statistics;
            Assert.AreEqual(2, statsBefore.TotalEnqueued, "应该记录2次入队");
            Assert.AreEqual(0, statsBefore.TotalProcessed, "处理前应该没有处理记录");
            Assert.AreEqual(2, statsBefore.CurrentQueueSize, "当前队列大小应该为2");

            eventQueue.ProcessAll(e => { /* 空处理器 */ });
            
            var statsAfter = eventQueue.Statistics;
            Assert.AreEqual(2, statsAfter.TotalProcessed, "应该记录2次处理");
            Assert.AreEqual(0, statsAfter.CurrentQueueSize, "处理后队列大小应该为0");
            Assert.AreEqual(1.0f, statsAfter.ProcessingRate, "处理率应该为100%");
        }

        /// <summary>
        /// 测试错误处理
        /// </summary>
        [Test]
        public void TestErrorHandling()
        {
            // Arrange
            eventQueue.Enqueue(new DebugEvent(testGameObject, "正常事件"));
            eventQueue.Enqueue(new DebugEvent(testGameObject, "错误事件"));

            // Act - 处理器抛出异常
            eventQueue.ProcessAll(e =>
            {
                if ((e as DebugEvent)?.Message == "错误事件")
                {
                    throw new InvalidOperationException("测试异常");
                }
                processedEvents.Add(e);
            });

            // Assert - 异常不应该阻止其他事件的处理
            Assert.AreEqual(1, processedEvents.Count, "正常事件应该被处理");
            Assert.IsTrue(eventQueue.IsEmpty, "队列应该被清空");
        }

        /// <summary>
        /// 测试清空操作
        /// </summary>
        [Test]
        public void TestClear()
        {
            // Arrange
            eventQueue.Enqueue(new DebugEvent(testGameObject, "事件1"));
            eventQueue.Enqueue(new DebugEvent(testGameObject, "事件2"));
            Assert.AreEqual(2, eventQueue.Count, "清空前应该有2个事件");

            // Act
            eventQueue.Clear();

            // Assert
            Assert.IsTrue(eventQueue.IsEmpty, "清空后队列应该为空");
            Assert.AreEqual(0, eventQueue.Count, "清空后计数应该为0");
            Assert.IsFalse(eventQueue.IsProcessing, "清空后不应该在处理状态");
        }

        /// <summary>
        /// 测试获取事件列表
        /// </summary>
        [Test]
        public void TestGetEvents()
        {
            // Arrange
            var event1 = new DebugEvent(testGameObject, "事件1");
            var event2 = new DebugEvent(testGameObject, "事件2");
            
            eventQueue.Enqueue(event1);
            eventQueue.Enqueue(event2);

            // Act
            var events = eventQueue.GetEvents();

            // Assert
            Assert.AreEqual(2, events.Count, "应该返回2个事件");
            Assert.Contains(event1, events, "应该包含事件1");
            Assert.Contains(event2, events, "应该包含事件2");
            
            // 修改返回的列表不应该影响原队列
            events.Clear();
            Assert.AreEqual(2, eventQueue.Count, "原队列不应该受到影响");
        }

        /// <summary>
        /// 测试统计重置
        /// </summary>
        [Test]
        public void TestResetStatistics()
        {
            // Arrange
            eventQueue.Enqueue(new DebugEvent(testGameObject, "事件1"));
            eventQueue.ProcessAll(e => { });
            
            var statsBefore = eventQueue.Statistics;
            Assert.Greater(statsBefore.TotalEnqueued, 0, "重置前应该有统计数据");

            // Act
            eventQueue.ResetStatistics();

            // Assert
            var statsAfter = eventQueue.Statistics;
            Assert.AreEqual(0, statsAfter.TotalEnqueued, "重置后入队计数应该为0");
            Assert.AreEqual(0, statsAfter.TotalProcessed, "重置后处理计数应该为0");
            Assert.AreEqual(0, statsAfter.TotalDropped, "重置后丢弃计数应该为0");
        }
    }
}
