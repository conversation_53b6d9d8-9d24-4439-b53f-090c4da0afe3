// 需求来源: 工作计划书.md 3.1 事件系统实现
// 高级异步事件处理器，支持超时、重试、回调链等功能

using System;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace Simulation.Core.Events
{
    /// <summary>
    /// 异步事件处理器配置
    /// </summary>
    [Serializable]
    public class AsyncEventHandlerConfig
    {
        [Header("超时设置")]
        public float timeoutSeconds = 30f;
        public bool enableTimeout = true;

        [Header("重试设置")]
        public int maxRetryCount = 3;
        public float retryDelaySeconds = 1f;
        public bool enableRetry = false;

        [Header("并发控制")]
        public int maxConcurrentHandlers = 10;
        public bool enableConcurrencyLimit = false;

        [Header("错误处理")]
        public bool continueOnError = true;
        public bool logErrors = true;

        [Header("性能监控")]
        public bool enablePerformanceTracking = true;
        public bool enableDetailedLogging = false;
    }

    /// <summary>
    /// 异步事件处理结果
    /// </summary>
    public class AsyncEventResult
    {
        public bool Success { get; set; }
        public Exception Exception { get; set; }
        public float ProcessingTime { get; set; }
        public int RetryCount { get; set; }
        public bool TimedOut { get; set; }
        public bool Cancelled { get; set; }

        public static AsyncEventResult CreateSuccess(float processingTime)
        {
            return new AsyncEventResult
            {
                Success = true,
                ProcessingTime = processingTime
            };
        }

        public static AsyncEventResult CreateFailure(Exception exception, float processingTime, int retryCount = 0)
        {
            return new AsyncEventResult
            {
                Success = false,
                Exception = exception,
                ProcessingTime = processingTime,
                RetryCount = retryCount
            };
        }

        public static AsyncEventResult CreateTimeout(float processingTime)
        {
            return new AsyncEventResult
            {
                Success = false,
                TimedOut = true,
                ProcessingTime = processingTime
            };
        }

        public static AsyncEventResult CreateCancelled(float processingTime)
        {
            return new AsyncEventResult
            {
                Success = false,
                Cancelled = true,
                ProcessingTime = processingTime
            };
        }
    }

    /// <summary>
    /// 高级异步事件处理器
    /// 支持超时、重试、并发控制、回调链等功能
    /// </summary>
    public class AsyncEventHandler<T> where T : EventBase
    {
        private readonly AsyncEventHandlerConfig config;
        private readonly List<Func<T, UniTask>> handlers = new List<Func<T, UniTask>>();
        private readonly List<Func<T, AsyncEventResult, UniTask>> resultCallbacks = new List<Func<T, AsyncEventResult, UniTask>>();
        private readonly SemaphoreSlim concurrencySemaphore;
        private readonly object lockObject = new object();

        // 统计信息
        private int totalProcessed = 0;
        private int totalSucceeded = 0;
        private int totalFailed = 0;
        private int totalTimedOut = 0;
        private int totalCancelled = 0;
        private float totalProcessingTime = 0f;

        public AsyncEventHandler(AsyncEventHandlerConfig config = null)
        {
            this.config = config ?? new AsyncEventHandlerConfig();
            
            if (this.config.enableConcurrencyLimit)
            {
                concurrencySemaphore = new SemaphoreSlim(this.config.maxConcurrentHandlers, this.config.maxConcurrentHandlers);
            }
        }

        /// <summary>
        /// 添加异步事件处理器
        /// </summary>
        public void AddHandler(Func<T, UniTask> handler)
        {
            if (handler == null) return;

            lock (lockObject)
            {
                handlers.Add(handler);
            }
        }

        /// <summary>
        /// 移除异步事件处理器
        /// </summary>
        public void RemoveHandler(Func<T, UniTask> handler)
        {
            if (handler == null) return;

            lock (lockObject)
            {
                handlers.Remove(handler);
            }
        }

        /// <summary>
        /// 添加结果回调
        /// </summary>
        public void AddResultCallback(Func<T, AsyncEventResult, UniTask> callback)
        {
            if (callback == null) return;

            lock (lockObject)
            {
                resultCallbacks.Add(callback);
            }
        }

        /// <summary>
        /// 处理异步事件
        /// </summary>
        public async UniTask<List<AsyncEventResult>> ProcessAsync(T eventData, CancellationToken cancellationToken = default)
        {
            var results = new List<AsyncEventResult>();
            List<Func<T, UniTask>> currentHandlers;

            lock (lockObject)
            {
                currentHandlers = new List<Func<T, UniTask>>(handlers);
                totalProcessed++;
            }

            if (currentHandlers.Count == 0)
            {
                return results;
            }

            if (config.enableDetailedLogging)
            {
                Debug.Log($"[AsyncEventHandler] Processing {typeof(T).Name} with {currentHandlers.Count} handlers");
            }

            // 并发控制
            if (config.enableConcurrencyLimit)
            {
                await concurrencySemaphore.WaitAsync(cancellationToken);
            }

            try
            {
                // 并行处理所有处理器
                var tasks = new List<UniTask<AsyncEventResult>>();
                
                foreach (var handler in currentHandlers)
                {
                    tasks.Add(ProcessSingleHandlerAsync(handler, eventData, cancellationToken));
                }

                // 等待所有处理器完成
                var handlerResults = await UniTask.WhenAll(tasks);
                results.AddRange(handlerResults);

                // 更新统计信息
                UpdateStatistics(results);

                // 执行结果回调
                await ExecuteResultCallbacks(eventData, results, cancellationToken);
            }
            finally
            {
                if (config.enableConcurrencyLimit)
                {
                    concurrencySemaphore.Release();
                }
            }

            return results;
        }

        /// <summary>
        /// 处理单个处理器
        /// </summary>
        private async UniTask<AsyncEventResult> ProcessSingleHandlerAsync(Func<T, UniTask> handler, T eventData, CancellationToken cancellationToken)
        {
            var startTime = Time.realtimeSinceStartup;
            int retryCount = 0;

            while (retryCount <= config.maxRetryCount)
            {
                try
                {
                    // 超时控制
                    if (config.enableTimeout)
                    {
                        using (var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(config.timeoutSeconds)))
                        using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
                        {
                            await handler(eventData).AttachExternalCancellation(combinedCts.Token);
                        }
                    }
                    else
                    {
                        await handler(eventData).AttachExternalCancellation(cancellationToken);
                    }

                    var processingTime = Time.realtimeSinceStartup - startTime;
                    return AsyncEventResult.CreateSuccess(processingTime);
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    var processingTime = Time.realtimeSinceStartup - startTime;
                    return AsyncEventResult.CreateCancelled(processingTime);
                }
                catch (TimeoutException)
                {
                    var processingTime = Time.realtimeSinceStartup - startTime;
                    return AsyncEventResult.CreateTimeout(processingTime);
                }
                catch (Exception ex)
                {
                    retryCount++;
                    var processingTime = Time.realtimeSinceStartup - startTime;

                    if (config.logErrors)
                    {
                        Debug.LogError($"[AsyncEventHandler] Error in handler (attempt {retryCount}): {ex.Message}");
                    }

                    // 如果达到最大重试次数或不启用重试，返回失败结果
                    if (!config.enableRetry || retryCount > config.maxRetryCount)
                    {
                        return AsyncEventResult.CreateFailure(ex, processingTime, retryCount - 1);
                    }

                    // 重试延迟
                    if (config.retryDelaySeconds > 0)
                    {
                        await UniTask.Delay(TimeSpan.FromSeconds(config.retryDelaySeconds), cancellationToken: cancellationToken);
                    }
                }
            }

            // 不应该到达这里
            var finalProcessingTime = Time.realtimeSinceStartup - startTime;
            return AsyncEventResult.CreateFailure(new InvalidOperationException("Unexpected end of retry loop"), finalProcessingTime, retryCount);
        }

        /// <summary>
        /// 执行结果回调
        /// </summary>
        private async UniTask ExecuteResultCallbacks(T eventData, List<AsyncEventResult> results, CancellationToken cancellationToken)
        {
            List<Func<T, AsyncEventResult, UniTask>> currentCallbacks;

            lock (lockObject)
            {
                currentCallbacks = new List<Func<T, AsyncEventResult, UniTask>>(resultCallbacks);
            }

            if (currentCallbacks.Count == 0) return;

            foreach (var result in results)
            {
                foreach (var callback in currentCallbacks)
                {
                    try
                    {
                        await callback(eventData, result).AttachExternalCancellation(cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        if (config.logErrors)
                        {
                            Debug.LogError($"[AsyncEventHandler] Error in result callback: {ex.Message}");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics(List<AsyncEventResult> results)
        {
            if (!config.enablePerformanceTracking) return;

            lock (lockObject)
            {
                foreach (var result in results)
                {
                    totalProcessingTime += result.ProcessingTime;

                    if (result.Success)
                    {
                        totalSucceeded++;
                    }
                    else if (result.TimedOut)
                    {
                        totalTimedOut++;
                    }
                    else if (result.Cancelled)
                    {
                        totalCancelled++;
                    }
                    else
                    {
                        totalFailed++;
                    }
                }
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public AsyncEventHandlerStatistics GetStatistics()
        {
            lock (lockObject)
            {
                return new AsyncEventHandlerStatistics
                {
                    TotalProcessed = totalProcessed,
                    TotalSucceeded = totalSucceeded,
                    TotalFailed = totalFailed,
                    TotalTimedOut = totalTimedOut,
                    TotalCancelled = totalCancelled,
                    AverageProcessingTime = totalProcessed > 0 ? totalProcessingTime / totalProcessed : 0f,
                    HandlerCount = handlers.Count,
                    CallbackCount = resultCallbacks.Count
                };
            }
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            lock (lockObject)
            {
                totalProcessed = 0;
                totalSucceeded = 0;
                totalFailed = 0;
                totalTimedOut = 0;
                totalCancelled = 0;
                totalProcessingTime = 0f;
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            lock (lockObject)
            {
                handlers.Clear();
                resultCallbacks.Clear();
            }

            concurrencySemaphore?.Dispose();
        }
    }

    /// <summary>
    /// 异步事件处理器统计信息
    /// </summary>
    [Serializable]
    public class AsyncEventHandlerStatistics
    {
        public int TotalProcessed;
        public int TotalSucceeded;
        public int TotalFailed;
        public int TotalTimedOut;
        public int TotalCancelled;
        public float AverageProcessingTime;
        public int HandlerCount;
        public int CallbackCount;

        public float SuccessRate => TotalProcessed > 0 ? (float)TotalSucceeded / TotalProcessed : 0f;
        public float FailureRate => TotalProcessed > 0 ? (float)TotalFailed / TotalProcessed : 0f;
        public float TimeoutRate => TotalProcessed > 0 ? (float)TotalTimedOut / TotalProcessed : 0f;
        public float CancellationRate => TotalProcessed > 0 ? (float)TotalCancelled / TotalProcessed : 0f;

        public override string ToString()
        {
            return $"处理: {TotalProcessed}, 成功: {TotalSucceeded}, 失败: {TotalFailed}, " +
                   $"超时: {TotalTimedOut}, 取消: {TotalCancelled}, 平均耗时: {AverageProcessingTime:F3}s, " +
                   $"成功率: {SuccessRate:P2}, 处理器数: {HandlerCount}, 回调数: {CallbackCount}";
        }
    }
}
