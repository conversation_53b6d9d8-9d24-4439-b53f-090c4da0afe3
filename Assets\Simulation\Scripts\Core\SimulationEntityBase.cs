// 需求来源: 工作计划书.md 3.2 实体接口标准化

using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Simulation.Core.Events;
using Simulation.Core.Interfaces;
using UnityEngine;

namespace Simulation.Core
{
    /// <summary>
    /// 所有模拟实体的抽象基类，实现ISimulationEntity接口
    /// </summary>
    public abstract class SimulationEntityBase : MonoBehaviour, ISimulationEntity
    {
        [Header("实体基础信息")]
        [SerializeField] private string entityName;
        [SerializeField] private bool isActive = true;

        private Guid _entityId;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isInitialized = false;
        private bool _isDestroyed = false;

        #region ISimulationEntity Implementation

        /// <summary>
        /// 实体的唯一标识符
        /// </summary>
        public Guid EntityId
        {
            get
            {
                if (_entityId == Guid.Empty)
                    _entityId = Guid.NewGuid();
                return _entityId;
            }
        }

        /// <summary>
        /// 实体名称
        /// </summary>
        public string EntityName
        {
            get => string.IsNullOrEmpty(entityName) ? GetType().Name : entityName;
            set => entityName = value;
        }

        /// <summary>
        /// 实体是否激活
        /// </summary>
        public bool IsActive
        {
            get => isActive && !_isDestroyed;
            set
            {
                if (isActive != value)
                {
                    isActive = value;
                    OnActiveStateChanged(value);
                }
            }
        }

        /// <summary>
        /// 实体是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 取消令牌，用于异步操作的取消控制
        /// </summary>
        protected CancellationToken CancellationToken => _cancellationTokenSource?.Token ?? default;

        #endregion

        #region Unity Lifecycle

        protected virtual void Awake()
        {
            _cancellationTokenSource = new CancellationTokenSource();
        }

        protected virtual void Start()
        {
            if (!_isInitialized)
            {
                Initialize();
            }
        }

        protected virtual void Update()
        {
            if (IsActive && _isInitialized)
            {
                OnUpdate();
            }
        }

        public virtual void OnDestroy()
        {
            if (!_isDestroyed)
            {
                OnEntityDestroy();
            }
        }

        #endregion

        #region ISimulationEntity Methods

        /// <summary>
        /// 初始化实体
        /// </summary>
        public virtual void Initialize()
        {
            if (_isInitialized)
            {
                Debug.LogWarning($"Entity {EntityName} is already initialized");
                return;
            }

            try
            {
                OnInitialize();
                _isInitialized = true;

                // 发布实体初始化事件
                PublishEvent(new EntityInitializedEvent(this, EntityId, EntityName));

                Debug.Log($"Entity {EntityName} ({EntityId}) initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to initialize entity {EntityName}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 每帧更新实体状态
        /// </summary>
        public virtual void OnUpdate()
        {
            // 子类重写此方法实现具体的更新逻辑
        }

        /// <summary>
        /// 销毁实体
        /// </summary>
        public virtual void OnEntityDestroy()
        {
            if (_isDestroyed) return;

            _isDestroyed = true;

            try
            {
                // 取消所有异步操作
                _cancellationTokenSource?.Cancel();

                // 调用子类的清理逻辑
                OnCleanup();

                // 发布实体销毁事件
                PublishEvent(new EntityDestroyedEvent(this, EntityId, EntityName));

                Debug.Log($"Entity {EntityName} ({EntityId}) destroyed successfully");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error during entity {EntityName} destruction: {ex.Message}");
            }
            finally
            {
                // 释放取消令牌源
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        #endregion

        #region Protected Virtual Methods

        /// <summary>
        /// 子类重写此方法实现具体的初始化逻辑
        /// </summary>
        protected virtual void OnInitialize()
        {
            // 默认实现为空，子类可以重写
        }

        /// <summary>
        /// 子类重写此方法实现具体的清理逻辑
        /// </summary>
        protected virtual void OnCleanup()
        {
            // 默认实现为空，子类可以重写
        }

        /// <summary>
        /// 当实体激活状态改变时调用
        /// </summary>
        /// <param name="newActiveState">新的激活状态</param>
        protected virtual void OnActiveStateChanged(bool newActiveState)
        {
            PublishEvent(new EntityActiveStateChangedEvent(this, EntityId, newActiveState));
        }

        #endregion

        #region Event Publishing

        /// <summary>
        /// 发布事件的便捷方法
        /// </summary>
        /// <param name="eventToPublish">要发布的事件</param>
        protected void PublishEvent(EventBase eventToPublish)
        {
            try
            {
                EventManager.Publish(eventToPublish);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to publish event {eventToPublish.GetType().Name} from entity {EntityName}: {ex.Message}");
            }
        }



        #endregion

        #region Utility Methods

        /// <summary>
        /// 检查实体是否可以执行操作
        /// </summary>
        /// <returns>如果实体已初始化、激活且未销毁则返回true</returns>
        protected bool CanPerformOperation()
        {
            return _isInitialized && IsActive && !_isDestroyed && !CancellationToken.IsCancellationRequested;
        }

        /// <summary>
        /// 安全执行异步操作的包装方法
        /// </summary>
        /// <param name="operation">要执行的异步操作</param>
        /// <param name="operationName">操作名称，用于日志记录</param>
        protected async UniTask SafeExecuteAsync(Func<CancellationToken, UniTask> operation, string operationName = "Unknown")
        {
            if (!CanPerformOperation())
            {
                Debug.LogWarning($"Entity {EntityName} cannot perform operation {operationName}: entity is not ready");
                return;
            }

            try
            {
                await operation(CancellationToken);
            }
            catch (OperationCanceledException)
            {
                Debug.Log($"Operation {operationName} was cancelled for entity {EntityName}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error in operation {operationName} for entity {EntityName}: {ex.Message}");
                throw;
            }
        }

        #endregion
    }

    #region Entity Events

    /// <summary>
    /// 实体初始化完成事件
    /// </summary>
    public class EntityInitializedEvent : EventBase
    {
        public Guid EntityId { get; }
        public string EntityName { get; }

        public EntityInitializedEvent(object sender, Guid entityId, string entityName) : base(sender)
        {
            EntityId = entityId;
            EntityName = entityName;
        }
    }

    /// <summary>
    /// 实体销毁事件
    /// </summary>
    public class EntityDestroyedEvent : EventBase
    {
        public Guid EntityId { get; }
        public string EntityName { get; }

        public EntityDestroyedEvent(object sender, Guid entityId, string entityName) : base(sender)
        {
            EntityId = entityId;
            EntityName = entityName;
        }
    }

    /// <summary>
    /// 实体激活状态改变事件
    /// </summary>
    public class EntityActiveStateChangedEvent : EventBase
    {
        public Guid EntityId { get; }
        public bool IsActive { get; }

        public EntityActiveStateChangedEvent(object sender, Guid entityId, bool isActive) : base(sender)
        {
            EntityId = entityId;
            IsActive = isActive;
        }
    }

    #endregion
}