// 需求来源: 工作计划书.md 3.1 事件系统实现
// 高级事件队列机制，避免递归调用问题，支持优先级和过滤

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Simulation.Core.Events
{
    /// <summary>
    /// 高级事件队列，支持优先级、过滤、递归避免等功能
    /// </summary>
    public class EventQueue
    {
        private readonly List<EventBase> events = new List<EventBase>();
        private readonly List<EventBase> processingBuffer = new List<EventBase>();
        private readonly object lockObject = new object();
        
        // 递归调用检测
        private bool isProcessing = false;
        private readonly Queue<EventBase> recursionBuffer = new Queue<EventBase>();
        
        // 队列配置
        private int maxQueueSize = 1000;
        private bool enablePrioritySort = true;
        private bool enableRecursionProtection = true;
        
        // 统计信息
        private int totalEnqueued = 0;
        private int totalProcessed = 0;
        private int totalDropped = 0;
        private int maxQueueSizeReached = 0;

        /// <summary>
        /// 队列中的事件数量
        /// </summary>
        public int Count
        {
            get
            {
                lock (lockObject)
                {
                    return events.Count + recursionBuffer.Count;
                }
            }
        }

        /// <summary>
        /// 队列是否为空
        /// </summary>
        public bool IsEmpty => Count == 0;

        /// <summary>
        /// 是否正在处理事件
        /// </summary>
        public bool IsProcessing
        {
            get
            {
                lock (lockObject)
                {
                    return isProcessing;
                }
            }
        }

        /// <summary>
        /// 队列统计信息
        /// </summary>
        public EventQueueStatistics Statistics
        {
            get
            {
                lock (lockObject)
                {
                    return new EventQueueStatistics
                    {
                        TotalEnqueued = totalEnqueued,
                        TotalProcessed = totalProcessed,
                        TotalDropped = totalDropped,
                        CurrentQueueSize = events.Count,
                        MaxQueueSizeReached = maxQueueSizeReached,
                        RecursionBufferSize = recursionBuffer.Count
                    };
                }
            }
        }

        /// <summary>
        /// 配置队列参数
        /// </summary>
        public void Configure(int maxSize = 1000, bool prioritySort = true, bool recursionProtection = true)
        {
            lock (lockObject)
            {
                maxQueueSize = maxSize;
                enablePrioritySort = prioritySort;
                enableRecursionProtection = recursionProtection;
            }
        }

        /// <summary>
        /// 将事件加入队列
        /// </summary>
        public bool Enqueue(EventBase eventToEnqueue)
        {
            if (eventToEnqueue == null) return false;

            lock (lockObject)
            {
                totalEnqueued++;

                // 检查队列大小限制
                if (events.Count >= maxQueueSize)
                {
                    totalDropped++;
                    Debug.LogWarning($"[EventQueue] 队列已满，丢弃事件: {eventToEnqueue.EventType}");
                    return false;
                }

                // 递归调用保护
                if (enableRecursionProtection && isProcessing)
                {
                    recursionBuffer.Enqueue(eventToEnqueue);
                    Debug.Log($"[EventQueue] 检测到递归调用，事件加入递归缓冲区: {eventToEnqueue.EventType}");
                    return true;
                }

                // 添加到主队列
                events.Add(eventToEnqueue);

                // 更新统计
                if (events.Count > maxQueueSizeReached)
                {
                    maxQueueSizeReached = events.Count;
                }

                // 优先级排序
                if (enablePrioritySort && events.Count > 1)
                {
                    SortByPriority();
                }

                return true;
            }
        }

        /// <summary>
        /// 处理队列中的所有事件
        /// </summary>
        public void ProcessAll(Action<EventBase> processor)
        {
            if (processor == null) return;

            List<EventBase> eventsToProcess;
            
            lock (lockObject)
            {
                if (events.Count == 0) return;

                // 设置处理标志
                isProcessing = true;

                // 复制事件列表
                eventsToProcess = new List<EventBase>(events);
                events.Clear();
            }

            try
            {
                // 处理事件
                foreach (var eventItem in eventsToProcess)
                {
                    try
                    {
                        processor(eventItem);
                        
                        lock (lockObject)
                        {
                            totalProcessed++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[EventQueue] 处理事件时发生错误: {eventItem.EventType}, 错误: {ex.Message}");
                    }
                }
            }
            finally
            {
                lock (lockObject)
                {
                    isProcessing = false;

                    // 处理递归缓冲区中的事件
                    while (recursionBuffer.Count > 0)
                    {
                        var recursiveEvent = recursionBuffer.Dequeue();
                        events.Add(recursiveEvent);
                    }

                    // 重新排序（如果有递归事件加入）
                    if (enablePrioritySort && events.Count > 1)
                    {
                        SortByPriority();
                    }
                }
            }
        }

        /// <summary>
        /// 处理指定数量的事件（分帧处理）
        /// </summary>
        public void ProcessBatch(int maxEvents, Action<EventBase> processor)
        {
            if (processor == null || maxEvents <= 0) return;

            List<EventBase> eventsToProcess = new List<EventBase>();

            lock (lockObject)
            {
                if (events.Count == 0) return;

                // 设置处理标志
                isProcessing = true;

                // 取出指定数量的事件
                int eventCount = Mathf.Min(maxEvents, events.Count);
                for (int i = 0; i < eventCount; i++)
                {
                    eventsToProcess.Add(events[i]);
                }
                events.RemoveRange(0, eventCount);
            }

            try
            {
                // 处理事件
                foreach (var eventItem in eventsToProcess)
                {
                    try
                    {
                        processor(eventItem);
                        
                        lock (lockObject)
                        {
                            totalProcessed++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[EventQueue] 处理事件时发生错误: {eventItem.EventType}, 错误: {ex.Message}");
                    }
                }
            }
            finally
            {
                lock (lockObject)
                {
                    isProcessing = false;

                    // 处理递归缓冲区中的事件
                    while (recursionBuffer.Count > 0)
                    {
                        var recursiveEvent = recursionBuffer.Dequeue();
                        events.Add(recursiveEvent);
                    }

                    // 重新排序（如果有递归事件加入）
                    if (enablePrioritySort && events.Count > 1)
                    {
                        SortByPriority();
                    }
                }
            }
        }

        /// <summary>
        /// 清空队列
        /// </summary>
        public void Clear()
        {
            lock (lockObject)
            {
                events.Clear();
                recursionBuffer.Clear();
                isProcessing = false;
            }
        }

        /// <summary>
        /// 获取队列中的事件列表（只读）
        /// </summary>
        public List<EventBase> GetEvents()
        {
            lock (lockObject)
            {
                return new List<EventBase>(events);
            }
        }

        /// <summary>
        /// 按优先级排序事件
        /// </summary>
        private void SortByPriority()
        {
            events.Sort((a, b) => a.CompareTo(b));
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            lock (lockObject)
            {
                totalEnqueued = 0;
                totalProcessed = 0;
                totalDropped = 0;
                maxQueueSizeReached = events.Count;
            }
        }
    }

    /// <summary>
    /// 事件队列统计信息
    /// </summary>
    [Serializable]
    public class EventQueueStatistics
    {
        public int TotalEnqueued;
        public int TotalProcessed;
        public int TotalDropped;
        public int CurrentQueueSize;
        public int MaxQueueSizeReached;
        public int RecursionBufferSize;

        public float ProcessingRate => TotalEnqueued > 0 ? (float)TotalProcessed / TotalEnqueued : 0f;
        public float DropRate => TotalEnqueued > 0 ? (float)TotalDropped / TotalEnqueued : 0f;

        public override string ToString()
        {
            return $"入队: {TotalEnqueued}, 处理: {TotalProcessed}, 丢弃: {TotalDropped}, " +
                   $"当前: {CurrentQueueSize}, 最大: {MaxQueueSizeReached}, 递归缓冲: {RecursionBufferSize}, " +
                   $"处理率: {ProcessingRate:P2}, 丢弃率: {DropRate:P2}";
        }
    }
}
