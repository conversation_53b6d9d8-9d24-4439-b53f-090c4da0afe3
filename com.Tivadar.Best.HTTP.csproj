﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace>Best.HTTP</RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>com.Tivadar.Best.HTTP</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_7;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;DOTWEEN;UNITASK_DOTWEEN_SUPPORT;ENVIRO_3;ENVIRO_URP;BESTHTTP_WITH_BURST;BESTHTTP_PROFILE;WITH_UNITASK;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.7f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\bzip2\CBZip2OutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\edec\EdECObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\nist\NISTObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509V3CertificateGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\Pkcs12Store.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSEnvelopedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\SubjectPublicKeyInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptopro\GOST3410PublicKeyAlgParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\PopoPrivKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathValidator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsSuiteHmac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ClientHello.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT283R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\CRLReason.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\tsp\TimeStampResp.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1InputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSSignedHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\AuthEnvelopedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TSPValidationException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT193R2Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Indexing\Comparers\ByteArrayComparer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Logger\ILogger.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\SCVPReqRes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\SP800SecureRandom.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\gnu\GNUObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\pem\PemObject.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsFatalAlert.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\V1TBSCertificateGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DHBasicKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Caching\HTTPCacheOptions.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcChaCha20Poly1305.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\SignaturePolicyId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\Ssl3Utilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IEncapsulatedSecretExtractor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\X25519Agreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT239Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\CertID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsClient.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Metadata.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9ECParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsStreamSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IEntropySource.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\JsonReader.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DefaultTlsSrpConfigVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsCloseable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509ObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\RipeMD128Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Caching\Builders.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\PrimeField.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\StreamBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSrpIdentityManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSrtpUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\RevRepContentBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\BaseOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\X25519PrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\ocsp\RequestedCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CipherType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\BaseKdfBytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CompleteRevocationRefs.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\Base64.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsHmac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ASN1StreamParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Threading\ThreadedRunner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\DefaultTls13Client.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\V2TBSCertListGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\ElGamalKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsECDheKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\EnvelopedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\RSAPublicKeyStructure.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\Timeout.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CrlValidatedID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\BasicOCSPResponse.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptopro\GOST28147Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\EncryptedValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\CamelliaWrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509V2AttributeCertificateGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\IHTTPRequestHandler.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\X448PublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AccessDescription.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\Iso4217CurrencyCode.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT233R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ASN1TaggedObjectParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TimeStampRequestGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IVerifierFactoryProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\Asn1DigestFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IAsymmetricBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\AuthenticatedSafe.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\TimeStampAndCRL.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x500\style\IetfUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\Attributes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\sec\ECPrivateKeyStructure.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\Streams\FrameworkTLSByteForwarder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\encodings\ISO9796d1Encoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat128.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT113Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastCcmBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\cert\CertificateExpiredException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\IStore.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\compression\ZLib.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\RecipientInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\IL2CPP\Il2CppSetOptionAttribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\UseSrtpData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\RC2Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DigestInputBuffer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\UrlBase64.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsPsk.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SymmetricEncDataPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BEROctetStringGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\OfbBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastTlsBlockCipherImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\HC256Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TSPUtil.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509V2CRLGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\UserMappingType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT131Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\microsoft\MicrosoftObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192K1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\IPolynomialExtensionField.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Kdf2BytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\Dstu7624WrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Authentication\Credentials.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RFC3394WrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TimeStampTokenGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\Controls.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\CertTemplateBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\util\BasicAlphabetMapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\PaddedBufferedBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ISecretWithEncapsulation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IDerivationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HeartbeatMessageType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsSrp6Server.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsECDHanonKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathChecker.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsAeadCipherImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\MD5Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ECLookupTable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\Restriction.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Ed448PrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Upload\Forms\UrlEncodedStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsHeartbeat.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\SimpleLookupTable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PollReqContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\JsonData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\Pkcs8EncryptedPrivateKeyInfoBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\EncryptedValueBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HTTP2ConnectionSettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ServerSrpParams.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AlertLevel.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsCredentialedAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DefiniteLengthInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\OCSPResponse.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\LimitedInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\SP800SecureRandomBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\BufferedDecoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSContentInfoParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ess\ContentHints.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\EntropyUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\SM2KeyExchangePublicParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerSet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsPskKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSrpIdentity.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\mozilla\PublicKeyAndChallenge.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\GOST3410DigestSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DsaPrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\SkeinParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\store\X509CertPairStoreSelector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCrlUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RC2Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\AuthenticatedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Objects.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\InfTree.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IAsymmetricCipherKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\ECNamedCurveTable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsDHUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\CfbBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Sha512tDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CertificateValues.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\SignerId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\RsaDigestSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\NameConstraints.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\HexTranslator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ess\ESSCertIDv2.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509Crl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CipherSuite.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DsaKeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsEncodeResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\Exportable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerTaggedObject.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\RevRepContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\BufferSegmentStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\SignerInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\PublicKeyFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IEntropySourceProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509Attributes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\KEKRecipientInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IVerifierFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP1\PeekableHTTP1Response.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\kdf\DHKekGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ECPublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Authentication\DigestStore.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\Deflate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT239FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcDefaultTlsCredentialedAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ISignerWithRecovery.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\Decompression\IDecompressor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\IPolynomial.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastBcChaCha20Poly1305.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ExporterLabel.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\jpake\JPakeParticipant.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptopro\ECGOST3410NamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP384R1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BERGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\ParameterUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT283Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsECDsaVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Tags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ICipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\ProtectedPkiMessageBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Haraka512Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RC4Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\DownloadContentStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PopoDecKeyRespContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\UnknownStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsCryptoException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\MonetaryLimit.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\IndefiniteLengthInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\JsonException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\EncryptedContentInfoParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\Evidence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSessionImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\store\X509CollectionStoreParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ExperimentalPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\EncryptionException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\ReversedWindowGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ContentType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Upload\DynamicUploadStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\BasicEntropySourceProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\ZDeflaterOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\OnePassSignaturePacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\PKCS5Scheme2PBEKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IKeyUnwrapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Utils\StreamUtil.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat192.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ParametersWithSalt.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAuthenticatedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT239K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\DefaultVerifierResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Timings\TimingEvent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\OutputStreamPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT131R2Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Timings\TimingEventInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DsaKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DsaValidationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\ECMqvWithKdfBasicAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509V1CertificateGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\ConnectionBase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\Translator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\KeyTransRecipientInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSStreamException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\pem\PemReader.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\Pkcs12Utilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\RevokedStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\HeartbeatManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\TeeOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\util\FilterStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP128R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerBMPString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AbstractTlsContext.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\RevAnnContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\abc\Tnaf.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509ExtensionsGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DLTaggedObjectParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsEncryptor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\SigPolicyQualifierInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509CrlEntry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BERSequenceParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\io\DigestSink.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\JSON.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\StaticTree.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsPskExternal.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\LazyDERSequence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsDH.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DesParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\Security.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\MemoryOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcX448.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\Pkcs12Entry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DefaultTlsDHGroupVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DefaultTlsClient.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\KeccakDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\IGcmMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\IBlockCipherMode.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\icao\CscaMasterList.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509Certificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\NestedMessageContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT409R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\CollectionUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ECCurveType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Cookies\CookieJar.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\BurstTables8kGcmMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\KDFCounterBytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\RevocationDetails.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RFC3211WrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TimeStampToken.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\Primes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat512.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\GlvTypeAParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\ProtectedPart.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IDSA.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\LazyDLEnumerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT131R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ess\ESSCertID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\util\CipherFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\DefaultVerifierCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\UserNotice.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\WNafUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSProcessableFile.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\extension\SubjectKeyIdentifierStructure.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DHKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DesEdeParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\SubjectDirectoryAttributes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\CertificatePair.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\PublicKeyAlgorithmTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\UserAttributePacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerSequence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\AgreementUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\Srp6StandardGroups.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TrustedAuthority.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\AEADParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\CertStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SignatureScheme.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat576.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\DataLengthException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastSalsa20Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\IssuingDistributionPoint.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Platform.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ConstructedBitStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DigitallySigned.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\smime\SMIMECapabilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ISO18033KDFParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\Ed448phSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\MD2Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\OriginatorId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\GenericKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\CryptoServicesRegistrar.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\MacUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\BCrypt.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\DigestUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\GOST3411_2012Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\Crc24.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\PrimaryUserId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\PacketTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\RevokedInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\IssuerSerial.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT283FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DsaKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BERBitString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\PasswordException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\UserAttributeSubpacketsReader.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsECDsa13Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\PEMReader.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Pkcs12ParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTPConnectionStates.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP521R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerTaggedObject.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\Autodetect\AndroidProxyDetector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\NewSessionTicket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IWrapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DLBitStringParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R2Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Kdf1BytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\FastTlsCrypto.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\DatabaseOptions.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\pem\PemHeader.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\X25519KeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\StreamOverflowException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\BasicGcmMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSrpLoginParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\PrimitiveEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\ISO9797Alg3Mac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\Tables8kGcmMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509V2AttributeCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\X923Padding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\CmsKeyTransRecipientInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\GOST3411_2012_512Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AbstractTlsKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Pkcs5S2ParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\smime\SMIMEAttributes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\Req.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsCryptoParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT283K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\GlvTypeBEndomorphism.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIHeaderBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcDefaultTlsCredentialedDecryptor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\RipeMD160Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\store\X509CrlStoreSelector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\gm\SM2P256V1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CommitmentTypeIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSSignedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IStreamCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509KeyUsage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ess\SigningCertificateV2.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9FieldID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\FixedPointUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SrpTlsClient.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\Hex.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CrlListID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IMacFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\DsaDigestSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\X448Agreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\AsymmetricKeyParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\GOST3410ParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HTTP2Response.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\BigIntegers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\JsonMapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\abc\ZTauElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerSet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\date\DateTimeUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\ECMqvBasicAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\DSAParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\RipeMD320Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTPProtocolFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\Rfc3281CertPathUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\PasswordRecipientInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\RecipientInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\RevReqContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\PrivateKeyFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT283K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\GeneratorUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\IGcmExponentiator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\gm\GMNamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Longs.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\InvalidParameterException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\EnvelopedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\Ed25519ctxSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ElGamalKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\ChaChaEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\Attribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSession.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerOctetString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\IFiniteField.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsCredentials.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\IExtensionField.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsRsaVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\CrlID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP384R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\XofUtils.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\FixedPointCombMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\srp\SRP6VerifierGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R2Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\BasicTlsPskExternal.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixNameConstraintValidatorException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerGeneralString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DeferredHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\ConnectionHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IDigestFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSSignedDataStreamGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\BufferHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509Extensions.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R2FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\ISAACEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\OCSPResponseStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertConfirmContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IEncapsulatedSecretGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\Time.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsHandshakeHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RC6Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\SignaturePolicyIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IBufferedCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastAesEngineHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\Ed448Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\SingleResp.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT571Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\IPreCompCallback.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\srp\SRP6Server.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OtherRevRefs.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\KeyAgreeRecipientInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsStreamSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\pem\PemGenerationException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DLTaggedObject.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DesKeyGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\PublicKeyPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\IssuerAndSerialNumber.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Upload\UploadStreamBase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IStreamCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT233K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerInteger.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsReliableHandshake.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT113R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\IDsaKCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\Decompression\GZipDecompressor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSrpConfigVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\PKMacBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\rfc7748\X25519.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\IBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\ServiceLocator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerNull.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsDsaSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\RecipientId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerStringBase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\DsaPublicBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\AesEngine_X86.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSPBEKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\EnvelopedDataHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\IAeadCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\BigInteger.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ConnectionEnd.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\RsaPrivateCrtKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\RevocationReason.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509DefaultEntryConverter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\HTTPProxyResponse.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\CircularBuffer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\AbstractECMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1ObjectDescriptor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT193FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ByteQueue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\ValidityPreCompInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsContext.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\SinglePubInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\HTTPRequestAsyncExtensions.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\CrmfException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\misc\NetscapeCertType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\CMSAttributes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\QCStatement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsECDomain.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\Deflate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\KeyUsage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\TnepresEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Text\StringBuilderPool.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\eac\EACObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\ConnectionEvents.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\OCBBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\V2Form.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DERSequenceParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\PreferredAlgorithms.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateVerify.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\tsp\Accuracy.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\KEKIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSrpUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ParametersWithRandom.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastSalsa20EngineHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\CertificateID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\GeneralSubtree.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\NoticeReference.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OcspListID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ASN1SequenceParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\ContentInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X962NamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Integers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509NameEntryConverter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\Inflate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509CertificateStructure.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\CertId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\kdf\ConcatenationKdfGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\DsaSecretBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ElGamalKeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\PrivateKeyInfoFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\Signature.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\TBSCertList.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\NaccacheSternEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163R2Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IBlockResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastChaCha7539EngineHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224K1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\extension\AuthorityKeyIdentifierStructure.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\tsp\TimeStampReq.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\djb\Curve25519.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ParametersWithID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\FileSystem\IIOService.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\Attribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\BlockCipherPadding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat448.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\KeyAgreeRecipientInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\TwofishEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\SignerInformationStore.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\X931SecureRandom.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Haraka256Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\bc\BCObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsSrp6VerifierGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\PreCompInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\PEMException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\encodings\Pkcs1Encoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\ZInflaterInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\GMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\OriginatorInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Arrays.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerIA5String.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1TaggedObject.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\Time.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Caching\HTTPCacheContentWriter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\HTTPRequestStates.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\InputStreamPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\Streams\FrameworkTLSStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsServerCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT283R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\PskTlsServer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ScaleXPointMap.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Utilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\OtherName.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ECPoint.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\srp\SRP6Utilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\OriginatorPublicKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\CfbBlockCipherMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\GeneralName.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\CertTemplate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ElGamalSecretBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Haraka256_X86.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\GOST3410ValidationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\BcpgObject.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\ProcurationSyntax.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\fpe\SP80038G.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\ReadOnlyBufferedStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\CrmfObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\GOST3410PublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\AbstractTlsCrypto.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DLSequence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsHandshakeRetransmit.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsCrypto.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\DigestRandomGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\MetaData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CmpCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsProtocol.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerNumericString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsDHConfig.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT571FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsBlockCipherImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\IL2CPP\Il2CppEagerStaticClassConstructionAttribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ECKeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\HTTPResponse.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\IRandomGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DEROctetStringParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\SubjectKeyIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\RSAPrivateKeyStructure.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\SignerLocation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\AuthenticatorControl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\Check.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\AesEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ECAlgorithms.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\rfc7748\X448Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ProtocolName.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\sigi\PersonalData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\ISISMTTObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\Streams\NonblockingUnderlyingStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HeartbeatExtension.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Sha256Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\Decompression\DeflateDecompressor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathValidatorUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\AbstractTlsSecret.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsEpoch.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ConstructedOctetStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\TimeStampedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\SignatureCreationTime.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\ECDHCBasicAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\TBSRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\OpenSSLPBEParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\gm\GMObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\CertificateConfirmationContentBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\LazyDLSequence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\PskTlsClient.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ICipherParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\BiometricData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\RFC3739QCObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT571K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIStatusInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\EmbeddedSignature.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\UrlAndHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\MqvPublicParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\SimpleAttributeTableGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\misc\CAST5CBCParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\SafeBag.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT571R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\CryptoApiRandomGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\AesFastEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\PushbackStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\TimeStampTokenEvidence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\RecipientInformationStore.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\TigerDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\CbcBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\AssemblyInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\ZeroBytePadding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ECNamedDomainParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\NonMemoableDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsSrp6VerifierGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\CompressionAlgorithmTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\BufferedStreamCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\IesWithCipherParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathValidatorResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Profiler\Memory\MemoryStats.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Settings\HostSettingsManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\HMacDsaKCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastGcmBlockCipherHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat160.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\teletrust\TeleTrusTObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DHParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\AttributeTable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\ZStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\DiskManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\CipherKeyGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsFatalAlertReceived.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsDsaVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSCompressedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsECConfig.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsRsaEncryptor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerOctetString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Ed25519PublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\PskIdentity.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\PBKDF2Params.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AttCertIssuer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\Rfc3280CertPathUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RC532Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\NaccacheSternKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AlgorithmIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixAttrCertPathValidator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DERSequenceGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\Decompression\BrotliDecompressor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\HMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Haraka512_X86.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsNullNullCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\util\Pack.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\IndexingService.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsReplayWindow.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\X448KeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\EncryptedPrivateKeyInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\bzip2\CBZip2InputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\CamelliaEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\ElGamalEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ECDomainParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\GOST3410KeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTPOverTCPConnection.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertChainType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\PrincipalUtil.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\Adler32.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\SubsequentMessage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\ZInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\IEncodable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsMacSink.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\io\MacSink.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsDHKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastPoly1305.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DsaParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\rfc7748\X25519Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\AsymmetricCipherKeyPair.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\PkcsIOException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Ed448KeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixAttrCertPathBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ECSecretBCPGKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\drbg\HashSP800Drbg.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AttributeTable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DERExternal.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\File\FileConnection.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DHParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DHPrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\CompressedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcDefaultTlsCredentialedSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\CRC\CRC32.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\Pfx.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Encodable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\OidTokenizer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DLBitString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\ISO7816d4Padding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSSignedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Ed25519PrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Indexing\Comparers\UInt32Comparer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\KeyPurposeId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\PolicyInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\CmsContentEncryptorBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\kisa\KISAObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\AdmissionSyntax.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\PEMWriter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\Streams\NonblockingBCTLSStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\RsaKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\nsri\NsriObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerBitString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerGeneralizedTime.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\GlvTypeAEndomorphism.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\CertificateStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\GlvTypeBParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsEccUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\X931Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\PublicSubkeyPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\GenRepContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\RsaPublicBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\IEncoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\io\DigestStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\PeekableStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\smime\SMIMECapabilitiesAttribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\KDFFeedbackBytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\OCSPUtil.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\DHParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\RecordPreview.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\RoleSyntax.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\RevocationDetailsBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\X448KeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\Pkcs10CertificationRequestDelaySigned.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\SimpleBlockResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\Packet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat384.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\RespData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\oiw\OIWObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\Challenge.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\LongArray.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\DHGroup.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat320.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\CertPolicyId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSUtils.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\Asn1CipherBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\MPInteger.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\VMPCMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\X448PrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\AuthEnvelopedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\InvalidCipherTextException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathValidatorException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509AttrCertParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RsaEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT113FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Upload\BodyLengths.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\VMPCKSA3Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BERSequenceGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\ChaCha20Poly1305.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509SignatureUtil.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\rfc8032\Ed448.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsClientContext.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\ISO10126d2Padding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsClientContextImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsBlockCipherImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\DesEdeEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DatagramReceiver.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsTransport.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ASN1Generator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DefaultTlsCredentialedSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\DNS\Cache\DNSCache.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\KDFFeedbackParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Memory\BufferStore.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Timings\TimingCollector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\ResponderID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ASN1SetParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\PKCS12StoreBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Null.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\IL2CPP\PreserveAttribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ChangeCipherSpec.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\JZlib.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\Implementations\SOCKSV5Negotiator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\cert\CertificateParsingException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\DhbmParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\BinaryReaders.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\ECDHWithKdfBasicAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\DefaultSignedAttributeTableGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Sha384Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1EncodableVector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ByteQueueInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT233K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertResponse.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\HKDFParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\icao\LDSSecurityObject.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsStreamVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RSABlindingEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\KCtrBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsNullCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DHKeyGeneratorHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT131FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\DeclarationOfMajority.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\TypeOfBiometricData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RSACoreEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ConstructedLazyDLEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SrtpProtectionProfile.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\KeyParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\ShortenedDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\Ed25519phSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\GOST28147Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\KeyAgreeRecipientInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\iana\IANAObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\SkeinEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PbmParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\EdDsaPublicBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TimeStampTokenInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\fpe\FpeEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\sigi\SigIObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP521R1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\Revocable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\WNafPreCompInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerBitStringParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Logger\FileOutput.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathBuilderException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DHParametersHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT239K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\CertificateRequestMessage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\srp\SRP6Client.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsServer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\IAsn1Convertible.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x500\Rdn.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HeaderTable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\Holder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP521R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SignatureSubpacketTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\jpake\JPakePrimeOrderGroup.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsSrp6Client.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ConstructedDLEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192R1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateStatusRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsCrypto.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\Ed25519Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ess\SigningCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SignatureAndHashAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateStatusType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\X25519KeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\PublicKeyEncSessionPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastAesEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerVisibleString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\WhirlpoolDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSEnvelopedDataGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\NaccacheSternKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\store\X509CollectionStore.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\ECDHBasicAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsECDsaSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ClientCertificateType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastTlsBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Authenticators\CredentialAuthenticator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\nist\KMACwithSHAKE128_params.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\IsoTrailers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\DHStandardGroups.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\ZlibBaseStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Settings\RetrySettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\SubjectPublicKeyInfoFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\PKIArchiveOptions.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Collections\ObjectModel\ObservableDictionary.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\GcmUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\NamedGroup.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\SipHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DesEdeKeyGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Ed448PublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\RC5Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Blake3Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsDheKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ECDsaPublicBCPGKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\Autodetect\EnvironmentProxyDetector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\PrfAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Ed25519KeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\CertificationRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Strings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\store\X509StoreFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1BitStringParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\WriteOnlyBufferedStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\SM4Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\IesEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Upload\Forms\MultipartFormDataStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIMessage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9ECPoint.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\Poly1305.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CompleteCertificateRefs.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BERSetGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HTTP2Frames.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Interleave.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\PrimitiveEncodingSuffixed.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptopro\CryptoProObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsDHanonKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\NamedGroupRole.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\ReadOnlySet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\InfBlocks.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\MD4Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\util\AlgorithmIdentifierFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP1\HTTP1ContentConsumer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\OpenBsdBCrypt.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IKeyWrapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\GZipStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAuthenticatedDataGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\KEKRecipientInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsHashSink.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat224.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509NameTokenizer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\PBES2Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\IJsonWrapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\DigestInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\SM2Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\PskKeyExchangeMode.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\IdeaEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\RsaSecretBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ServerOnlyTlsAuthentication.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\CamelliaLightEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\MqvPrivateParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsDHGroupVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\IdentifierType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\rosstandart\RosstandartObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\SM2KeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\ISelector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Indexing\Comparers\UInt16Comparer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TimeStampResponse.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsSecret.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\EncryptedContentInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcX25519.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\EncKeyWithID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Settings\AsteriskStringComparer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\IssuerKeyId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsImplUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\CcmBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ISigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT113R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\BufferedAeadBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DLSet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\icao\DataGroupHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertOrEncCert.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HandshakeType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\ParserToken.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\IPKMacPrimitivesProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTls13Verifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\GOST3411Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\OriginatorInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ServerHello.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\RegTokenControl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ExtensionType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ECDHPublicBCPGKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224K1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Memory\AutoReleaseBuffer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Logger\ThreadedLogger.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixPolicyNode.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\PKMacValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\SemanticsInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateUrl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\BcpgOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\BufferedAeadCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Manager\HostKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\BasicTlsPskIdentity.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerVideotexString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastTlsAeadCipherImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsNonceGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\djb\Curve25519Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\anssi\ANSSINamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\ECKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\Pkcs8EncryptedPrivateKeyInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastChaChaEngineHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\DHStandardGroups.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\GOST28147Mac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1GeneralizedTime.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\FiniteFields.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Indexing\Comparers\StringComparer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSTypedStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\DNS\Cache\DNSCacheEntry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\ProofOfPossession.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\Timer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\RespID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HTTP2ContentConsumer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Bytes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Caching\HTTPCacheDatabase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT113R2Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\Tables1kGcmExponentiator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\OtherInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\SM2KeyExchangePrivateParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsPskIdentity.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\ZTree.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\PolicyQualifierId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CrlOcspRef.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerPrintableString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RC2WrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\SEEDWrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsPeer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\SkeinMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\RandomDsaKCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509Attribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\IX509Extension.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\HTTPRange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\KeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\BasicConstraints.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Manager\HostManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ContainedPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Blake2sDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerBoolean.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAttributeTableGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SignatureSubpacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\Iso9796d2Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\HTTPProxy.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\XTEAEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\BasicTlsSrpIdentity.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\LazyASN1InputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\BlockingDownloadContentStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\HeaderParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\CertificatePolicies.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\StoreImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\PEMUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\rfc7748\X448.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Bits.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateStatusRequestItemV2.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224R1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\CryptoHashAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\abc\SimpleBigDecimal.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsDHDomain.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\FpeParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\WebGL\WebGLXHRNativeConnectionLayer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\Decompression\DecompressorFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\KeyDerivationFunc.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\OCSPRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\ETSIQCObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SrpTlsServer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SignatureSubpacketsReader.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\drbg\ISP80090Drbg.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\ZTree.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT409K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\drbg\HMacSP800Drbg.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HeartbeatMessage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsDHDomain.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\MiscPemGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\RC2CBCParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\kdf\ECDHKekGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ArmoredOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\MaxFragmentLength.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\qualified\MonetaryValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\VMPCRandomGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HandshakeMessageOutput.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Blake3Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CommitmentTypeQualifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RC564Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\sec\SECObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\V3TBSCertificateGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\WTauNafMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\ReasonFlags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\OtherKeyAttribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IBasicAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\util\Asn1Dump.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\ResponseData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsRsaPssSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ByteQueueOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HTTP2SettingsRegistry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\EcbBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\PBEParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\SOCKSProxy.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\NotationData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9IntegerConverter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\AttributeCertificateIssuer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP384R1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\NoekeonEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TimeStampRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\OCSPException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\IDsaEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerUTCTime.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CommitmentTypeIndication.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSEnvelopedGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SupplementalDataEntry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\TbcPadding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\jpake\JPakeRound1Payload.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\Tables4kGcmMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\gm\SM2P256V1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP1\Constants.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\KeySpecificInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT233FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\Asn1Signature.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\jpake\JPakePrimeOrderGroups.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\EncryptedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\KCcmBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsECDomain.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsServerContextImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\SHA3Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAuthenticatedGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\UnsupportedPacketVersionException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\tsp\TSTInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\TargetInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\SignerUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SessionParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\ChaCha7539Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastSicBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192K1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\EndoPreCompInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HTTP2Stream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\drbg\CtrSP800Drbg.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\ScalarSplitParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\ContentInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastTlsAeadCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\CmpException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ECPublicBCPGKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\cert\CrlException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\JsonWriter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AttributeCertificateInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\IMemoable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ECPointMap.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\Srp6Group.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\srp\SRP6StandardGroups.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\EncryptedKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\KeyLogFileWriter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\io\SignerStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\LegacyTls13Verifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\Certificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Profiler\Network\NetworkStats.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SignatureAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\OCSPReqGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256R1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\Cast6Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\Tls13Verifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSReadable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\GOST3410Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\KeyTransRecipientInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\InfTree.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\SicBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\KeyAgreeRecipientIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Shorts.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\PasswordRecipientInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x500\AttributeTypeAndValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateEntry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\OriginatorIdentifierOrKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\ProofOfPossessionSigningKeyBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\JksStore.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\RsaKeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ParametersWithSBox.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ISignatureFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1OctetString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsEd448Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OtherSigningCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\smime\SMIMEEncryptionKeyPreferenceAttribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\ECMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509CrlParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsProtocol.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Memory\BufferSegment.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Caching\HTTPCache.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Blake2xsDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SimulatedTlsSrpIdentityManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256K1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\ecc\MQVuserKeyingMaterial.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\RecipientKeyIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\EdSecretBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerApplicationSpecific.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\ESFAttributes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\PrivateKeyUsagePeriod.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\RecordStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\SEEDEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\IAsn1Encoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\IssuerAndSerialNumber.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\HashAlgorithmTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT409Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\PkiArchiveControlBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\SignerInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Indexing\Comparers\DateTimeComparer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\TCPStreamer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\sigi\NameOrPseudonym.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\IAeadBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\LazyDLSet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DHKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\Admissions.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SecurityParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\SCrypt.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsECDHKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsRsaKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\IetfAttrSyntax.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\Dstu7624Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixNameConstraintValidator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\OCSPObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\TrustPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\jpake\JPakeUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R2Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\CertificationRequestInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIFreeText.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\nist\KMACwithSHAKE256_params.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\PbeUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\ParallelHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Enums.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PollRepContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsRsaPssVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ServerName.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\RsaKeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\ObjectDigestInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\DefaultSignatureResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsECDH.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HeartbeatMode.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\SecurityUtilityException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\ExtendedKeyUsage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\RSASSAPSSparams.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Settings\ProxySettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\icao\LDSVersionInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\encodings\OaepEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\ZOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\teletrust\TeleTrusTNamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DefaultTlsHeartbeat.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\HTTPMethods.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastChaChaEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\BinaryWriters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CrlStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\NullEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\RevocationReasonTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerT61String.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Settings\DownloadSettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\Interfaces.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSEnvelopedDataStreamGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\CompressedDataPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\misc\MiscObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\Future.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSProcessableByteArray.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\pem\PemObjectGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1UniversalType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\SerpentEngineBase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192R1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\bc\LinkedCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT193Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\OfferedPsks.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsSrpConfig.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Ed448KeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\StandardDsaEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ess\ContentIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\ECGOST3410Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509Name.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptopro\ECGOST3410ParamSetParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAuthEnvelopedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\HTTPUpdateDelegator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerUniversalString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\tsp\MessageImprint.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerObjectIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsHmac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Pkcs5S1ParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ConstructedILEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HandshakeMessageInput.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\EncryptedPrivateKeyInfoFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\TEAEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT131R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\RSABlindingParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\misc\NetscapeRevocationURL.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\AesUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\CertificateList.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsDssSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OtherHashAlgAndValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\DotNetUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\FramesAsStreamView.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\IThreadSignaler.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\TrustSignature.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\Pkcs10CertificationRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SignaturePacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\DsaParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\RevocationKeyTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\DHAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertifiedKeyPair.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\SignedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP128R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\DHPublicKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\compression\Bzip2.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\BufferedCipherBase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\GeneralNames.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\pem\PemObjectParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\PkcsException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9ECParametersHolder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OtherCertID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\CtsBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IDerivationFunction.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\pem\PemWriter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IRsa.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DHKeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\CertificateStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HTTP2FrameHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsAeadCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\io\CipherStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SecretSubkeyPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAttributeTableGenerationException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256K1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\nist\NISTNamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\BaseInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT571K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSEnvelopedHelper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsCredentialedDecryptor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RSABlindedEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\HTTPManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP384R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ASN1OctetStringParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\NullDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertAnnContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP128R1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\OtherRevocationInfoFormat.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\MemoryInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CmpObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsClientProtocol.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\EncryptionScheme.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OtherHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\HKDFBytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\SignatureExpirationTime.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\HTTPRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\rfc8032\Ed25519.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\MetadataIndexFinders\FindDeletedMetadataIndexFinder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Collections\Specialized\NotifyCollectionChangedEventArgs.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ECCurve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcSsl3Hmac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsRecordLayer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\DHValidationParms.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerUTF8String.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\Zlib.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIMessages.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ElGamalParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\OobCert.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\Streams.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\Negotiator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\CertificateRequestMessageBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\bzip2\CRC.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CompressionMethod.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\SM3Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAuthenticatedDataStreamGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\ProtectedPkiMessage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsSrp6Client.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\WTauNafPreCompInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\AbstractECLookupTable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\S2k.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OtherRevVals.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\CMSObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Threading\LockHelpers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\SignerInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\SignerUserId.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerOutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT409R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Blake2bDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\bzip2\BZip2Constants.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsCertificateRole.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\UserAttributeSubpacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\X509Extension.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\DesEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\BufferPoolMemoryStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1UniversalTypes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\SM2Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\Tables64kGcmMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\GeneralSecurityException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\ShakeDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\LazyDERSet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcVerifyingStreamSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\BasicOCSPResp.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\SerpentEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\VMPCEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsRsaSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\MaxBytesExceededException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DSAParameterGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1UtcTime.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\Autodetect\ProgrammaticallyAddedProxyDetector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\Asn1KeyWrapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\IDigestCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\PlainDsaEncoding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\KeyExchangeAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\ThreefishEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\MarkerPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X962Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\ElGamalParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\UserAttributeSubpacketTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\CertReqMessages.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IMacDerivationFunction.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\EAXBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IDecryptorBuilderProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerEnumerated.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\NameType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Settings\TimeoutSettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\KeyExpirationTime.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\CryptoApiEntropySourceProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSSignedGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\ocsp\CertHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerSequence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ECPrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\RecipientIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\ReadOnlyDictionary.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\OCSPResp.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcX25519Domain.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Sha1Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\AbstractTls13Client.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\HeaderValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\AdditionalInformationSyntax.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSSignedDataGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\CSHAKEDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OcspResponsesID.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsServerContext.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\kdf\DHKdfParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\jpake\JPakeRound3Payload.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\io\SignerSink.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\cert\CertificateException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\UrlBase64Encoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\GenericSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CounterSignatureDigestCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\KDFCounterParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsNonceGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\AttributeTypeAndValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastGcmBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\CertStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\GOST3410Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\HashAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIBody.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\CryptoException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\operators\DefaultSignatureCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\UserIdPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Authentication\WWWAuthenticateHeaderParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\store\X509AttrCertStoreSelector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\ReadOnlyList.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT131R2Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\HarakaBase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ServerNameList.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\cert\CertificateEncodingException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Sha512Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\OCSPRespGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPath.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\DistributionPoint.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\TeeInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\Iso9796d2PssSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TimeStampResponseGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\drbg\DrbgUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Indexing\Comparers\Hash128Comparer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAuthenticatedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\RevocationValues.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\KeyValuePairList.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\RSAESOAEPparams.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\oiw\ElGamalParameter.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsServerProtocol.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerApplicationSpecific.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IXof.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\BufferedIesCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsServerProtocol.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixBuilderParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\InfoTypeAndValue.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsCredentialedSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\PeekableIncomingSegmentStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\Streams\NonblockingTCPStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\PKCS5Scheme2UTF8PBEKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcX448Domain.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\GeneralPkiMessage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AbstractTlsServer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\Lexer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DefaultTlsServer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIHeader.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptopro\GOST3410NamedParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\RevocationKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DERGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\StreamList.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509ExtensionBase.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\PkiArchiveControl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsSrp6Server.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Indexing\AVLTree.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT193R2Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ua\UAObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\HC128Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\sec\SECNamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ECFieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT233R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsServerCertificateImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\RsaUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\WebGL\WebGLXHRNativeInterface.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HuffmanEncoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP521R1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\CRLNumber.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AuthorityKeyIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\SignerAttribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSCompressedDataStreamGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\ReadOnlyCollection.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\FilterStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\BufferedBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\EncryptionAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertReqTemplateContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptlib\CryptlibObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\fpe\FpeFf1Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CrlSource.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ParametersWithIV.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\LimitedInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AttributeCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\KDFDoublePipelineIterationBytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\net\IPAddress.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSSecureReadable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\GenMsgContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\Srp6GroupParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DHPublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\AesLightEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsPskIdentityManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\gcm\BasicGcmExponentiator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\IDownloadContentBufferAvailable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ScaleXNegateYPointMap.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\KeyFlags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\smime\SMIMECapability.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Logger\UnityOutput.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsKeyExchangeFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\GOFBBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\IBlockCipherPadding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Tag.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\Target.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\BufferedEncoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\TupleHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\OtherRecipientInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\sig\Features.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\extension\X509ExtensionUtil.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\KeyException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\SignatureException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\ECNRSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\RSABlindingFactorGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DefaultTlsKeyExchangeFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Settings\HostSettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\Proxy.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\Attribute.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\RequestEvents.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\Pkcs8Generator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT193R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\OCSPRespStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\RipeMD256Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\KDFDoublePipelineIterationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ScaleYPointMap.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\GcmSivBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\MetadataService.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\misc\VerisignCzagExtension.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSCompressedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\HexEncoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\icao\ICAOObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\OobCertHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\HTTP2\HPACKEncoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ECPointFormat.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AbstractTlsKeyExchangeFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT571R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\ZlibCodec.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AbstractTlsClient.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SymmetricKeyAlgorithmTags.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509CertificatePair.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\IAsn1Choice.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\SupplementalDataType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\CRLDistPoint.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Settings\RedirectSettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\DefaultPKMacPrimitivesProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\io\compression\Zip.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ElGamalPublicBcpgKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSCompressedDataGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\FileSystem\DefaultIOService.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\Database.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\GlvEndomorphism.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\SkipjackEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\cert\CertificateNotYetValidException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\KMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AuthorityInformationAccess.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsTimeoutException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\EncryptedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsSecret.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\MacData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Logger\LoggingContext.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\EnumerableProxy.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\X931SecureRandomBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SymmetricEncIntegrityPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\anssi\ANSSIObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\InfCodes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Upload\JSonDataStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\IAsn1String.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\FixedPointPreCompInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\GOST3410PrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSEnvelopedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DERSetGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsRawKeyCertificate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Exception.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\PopoSigningKeyInput.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Sequence.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\GOST3410KeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1RelativeOid.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\SignedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CrlAnnContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\BcpgInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\openssl\IPasswordFinder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AlertDescription.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DERSetParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\Request.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DtlsReassembler.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\KeyRecRepContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\DSTU7564Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Connections\WebGL\WebGLXHRConnection.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsDecodeResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\KeyTransRecipientInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Type.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\OptionalValidity.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\RecordFormat.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Settings\UploadSettings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\PrivateKeyInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsAuthentication.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\RecipientInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\attr\ImageAttrib.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CombinedHash.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\AttributeCertificateHolder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\jpake\JPakeRound2Payload.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\IControl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\IDrbgProvider.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\PbeParametersGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\paddings\Pkcs7Padding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\prng\X931Rng.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsClientProtocol.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertRepMessage.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\LinkedDictionary.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsStreamVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DatagramSender.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BERTaggedObjectParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CertificateCompressionAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Set.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\ScaleYNegateXPointMap.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastCbcBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ClientAuthenticationType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Timings\TimingEventNames.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Network\Tcp\TCPRingmaster.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\SingleResponse.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIFailureInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CertStatus.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\NaccacheSternKeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\GCMBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\AsymmetricKeyEntry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\TweakableBlockCipherParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\EndoUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\CbcBlockCipherMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\InvalidKeyException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DerGraphicString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Streams\PeekableContentProviderStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\BaseDigestCalculator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\FreeListManager.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\Autodetect\ProxyDetector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Mgf1BytesGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\NaccacheSternPrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Extensions\Extensions.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSProcessable.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\KdfParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\DistributionPointName.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP224R1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\GOST3411_2012_256Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsExtensionsUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\CrlIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP256R1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\RootCaKeyUpdateContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Settings\Node.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\KEKRecipientInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\AbstractTlsPeer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ICipherBuilderWithKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Poly1305KeyGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsEd25519Signer.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ec\CustomNamedCurves.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\SignerInformation.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Response\HTTPStatusCodes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Authentication\Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\DSTU7624Mac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\TBSCertificateStructure.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\esf\OcspIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\gm\SM2P256V1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\GlvMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\SkeinDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\TlsSuiteMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\MacAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkcs\X509CertificateEntry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\AriaEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\OcspStatusRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509CertPairParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BERSetParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\ProfessionInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\PKCS12PBEParams.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\PKCSObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ElGamalPublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\generators\Ed25519KeyPairGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DHValidationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\TrustAnchor.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BEROctetStringParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cryptopro\GOST3410ParamSetParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SecretKeyPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\DeflateStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\util\CipherKeyGeneratorFactory.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\agreement\DHBasicAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\Cast5Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ModDetectionCodePacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT193R1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\AuthenticatedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\X9ObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ocsp\ResponseBytes.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IAlphabetMapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Mod.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\OriginatorInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\fpe\FpeFf3_1Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsNoCloseNotifyException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PopoDecKeyChallContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Authenticators\BearerTokenAuthenticator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1Object.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\GenericPolynomialExtensionField.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT409FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\CAKeyUpdAnnContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TSPAlgorithms.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixCertPathBuilderResult.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\collections\HashSet.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\DsaPublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\MetadataIndexFinders\IEmptyMetadataIndexFinder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSAuthEnvelopedGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\PasswordRecipientInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\Salsa20Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\BerApplicationSpecificParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\Grain128AEADEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\GOST3410KeyGenerationParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\BasicOCSPRespGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP160R1Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\PEMParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\Spans.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\Targets.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\DERExternalParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\ECDsaSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT163R2Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1ParsingException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ECGOST3410Parameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\modes\OpenPgpCfbBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ProtocolVersion.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\TLS\Crypto\Impl\FastChaCha7539Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\MgfParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\DesEdeWrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\RijndaelEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT233Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\CachedInformationType.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\LiteralDataPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsDssVerifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\BufferedAsymmetricBlockCipher.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\LongDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\SecureRandom.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\IRawAgreement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ElGamalPrivateKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\endo\ECEndomorphism.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\multiplier\WNafL2RMultiplier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\isismtt\x509\NamingAuthority.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\bsi\BsiObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509Utilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\BlowfishEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\XSalsa20Engine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\GeneralDigest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\zlib\Inflate.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\PKIConfirmContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\RecipientEncryptedKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\ntt\NTTObjectIdentifiers.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\ECKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\SymmetricKeyEncSessionPacket.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\PkixAttrCertChecker.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Cookies\Cookie.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Proxies\Autodetect\FrameworkProxyDetector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\digests\Sha224Digest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\PKIPublicationInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\DisplayText.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\CompressedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP128R1FieldElement.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\bcpg\ArmoredInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\WrapperUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\DatagramTransport.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecP192K1Field.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT113R2Point.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\SignedData.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\OutputLengthException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\pkix\ReasonsMask.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\TlsSrpKeyExchange.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\Database\MetadataIndexFinders\DefaultEmptyMetadataIndexFinder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\encoders\Base64Encoder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\PopoSigningKey.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\PssSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\ocsp\OCSPReq.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\util\MemoableResetException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\DSTU7564Mac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\CertRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\ErrorMsgContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x500\DirectoryString.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\DefaultAuthenticatedAttributeTableGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\macs\CMac.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crmf\IEncryptedValuePadder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\gm\SM2P256V1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\TlsCryptoUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\AttCertValidityPeriod.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\KeyShareEntry.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\JSON\LitJson\JsonMockWrapper.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\engines\AesWrapEngine.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\SignerIdentifier.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Shared\PlatformSupport\Memory\BufferPool.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\V2AttributeCertificateInfoGenerator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\field\GF2Polynomial.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\PolicyMappings.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\CryptoSignatureAlgorithm.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\pkcs\CertBag.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Request\Authenticators\IAuthenticator.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cmp\CertificateConfirmationContent.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\signers\DsaSigner.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\KeyUpdateRequest.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\TimeStampedDataParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x509\PolicyQualifierInfo.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\cms\CMSProcessableInputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\crypto\impl\bc\BcTlsAeadCipherImpl.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tls\ChannelBinding.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cmp\RevDetails.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\security\CipherUtilities.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\x9\DHDomainParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\cms\ContentInfoParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\Asn1OutputStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\TSPException.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\io\MacStream.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\IesParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\ICipherBuilder.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\smime\SMIMECapabilityVector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\HTTP\Hosts\Manager\HostVariant.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\raw\Nat256.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\store\X509CertStoreSelector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\Compression\Zlib\ZlibConstants.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\crmf\CertReqMsg.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\math\ec\custom\sec\SecT409K1Curve.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\x509\X509CertificateParser.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\asn1\misc\IDEACBCPar.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\Profiler\Network\NetworkStatsCollector.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\crypto\parameters\X25519PublicKeyParameters.cs" />
    <Compile Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\tsp\GenTimeAccuracy.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Packages\com.tivadar.best.http\Runtime\com.Tivadar.Best.HTTP.asmdef" />
    <None Include="Packages\com.tivadar.best.http\Runtime\csc.rsp" />
    <None Include="Packages\com.tivadar.best.http\Runtime\3rdParty\BouncyCastle\License.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Ply">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Ply.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LibTessDotNet">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Dependencies\LibTessDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.HDRLoader">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.HDRLoader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="IxMilia.ThreeMf">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Dependencies\IxMilia.ThreeMf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.SharpZipLib">
      <HintPath>Library\PackageCache\com.unity.sharp-zip-lib@6b61f82b0cb3\Runtime\Unity.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Textures">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Textures.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.ThreeMf">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.ThreeMf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SafeStbImageSharp">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Dependencies\SafeStbImageSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="protobuf-net">
      <HintPath>Assets\Battlehub\Protobuf.Net\protobuf-net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Stl">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Stl.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityWeld">
      <HintPath>Assets\Battlehub\RTEditor\ThirdParty\UnityWeld\UnityWeld.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Fbx">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Fbx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="StorageTypeModel">
      <HintPath>Assets\Battlehub\StorageData\Generated\StorageTypeModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Gltf.Draco">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Gltf.Draco.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Gltf">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Gltf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Obj">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Obj.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Dae">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Dae.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets\Plugins\DOTween\Editor\DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets\Battlehub\RTEditor\ThirdParty\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Jint">
      <HintPath>Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Jint.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Acornima">
      <HintPath>Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Acornima.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Profiling.Core">
      <HintPath>Library\ScriptAssemblies\Unity.Profiling.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask">
      <HintPath>Library\ScriptAssemblies\UniTask.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
