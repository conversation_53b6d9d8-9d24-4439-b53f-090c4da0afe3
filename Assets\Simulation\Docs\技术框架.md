# 建筑工地数字孪生模拟沙盘技术框架 V3.0 - 简化版

## 1. 项目愿景与目标

本项目旨在构建一个简单高效的建筑工地数字孪生环境，专注于数据生成。核心目标是模拟工地现场的各类动态活动，生成结构化的时序数据，为AI训练和数据分析提供支持。项目的设计哲学是**简单高效，敏捷开发**，专注业务逻辑实现，避免过度工程化。

---

## 2. 系统核心架构

系统采用**简化的模块化**架构设计，每个模块专注于特定的业务领域，使用直接的方法调用进行通信。摒弃复杂的事件系统和接口继承，使用异步函数保持业务逻辑的高内聚性。

### 2.1. 简化架构图

```mermaid
flowchart TD
    %% 业务模块层
    subgraph Business ["🎯 业务模块层"]
        C1["👷 工人管理<br/>Worker Manager"]
        C2["🚛 车辆管理<br/>Vehicle Manager"]
        C3["🏗️ 设备管理<br/>Equipment Manager"]
        C4["🌫️ 环境监测<br/>Environment Monitor"]
        C5["🌤️ 气象模拟<br/>Weather Simulator"]
        C6["🚪 门禁管理<br/>Access Control"]
        C7["📦 物料管理<br/>Material Manager"]
        C8["📊 数据导出<br/>Data Exporter"]
    end

    %% 直接方法调用关系
    C5 -.->|"直接调用"| C4
    C5 -.->|"直接调用"| C1
    C1 -.->|"直接调用"| C4
    C1 -.->|"直接调用"| C3
    C1 -.->|"直接调用"| C6
    C2 -.->|"直接调用"| C4
    C2 -.->|"直接调用"| C6
    C3 -.->|"直接调用"| C4
    C1 -.->|"直接调用"| C2
    C1 -.->|"直接调用"| C7
    C3 -.->|"直接调用"| C7
    C7 -.->|"直接调用"| C2

    %% 数据收集
    C1 -.->|"数据收集"| C8
    C2 -.->|"数据收集"| C8
    C3 -.->|"数据收集"| C8
    C4 -.->|"数据收集"| C8
    C5 -.->|"数据收集"| C8
    C6 -.->|"数据收集"| C8
    C7 -.->|"数据收集"| C8
```

### 2.2. 核心设计原则

-   **简化架构**: 移除复杂的事件系统，使用直接的方法调用进行模块间通信
-   **异步优先**: 使用异步函数（UniTask）保持业务逻辑的高内聚性，避免复杂的状态机
-   **专注业务**: 每个管理器专注于特定领域的业务逻辑，不使用接口和继承
-   **数据导向**: 重点关注数据生成和导出，简化非核心功能


---

## 3. 业务模块设计

### 3.1. 工人管理模块 (Worker Manager)

#### 3.1.1 核心功能
-   **异步工作流程**: 使用异步函数组织完整的工作流程（上班→工作→休息→下班），避免复杂的状态机
-   **简单移动控制**: 基于Unity Transform的直接移动控制，不使用复杂的导航系统
-   **安全违规模拟**: 简单的概率模型模拟安全装备违规，生成违规数据
-   **工种管理**: 支持8种基本工种，每种工种有不同的行为特征
-   **数据记录**: 实时记录工人状态、位置、安全装备等数据

#### 3.1.2 异步工作流程

```mermaid
flowchart TD
    A["工人初始化"] --> B["开始异步工作流程"]
    B --> C["等待上班时间"]
    C --> D["移动到工作区域"]
    D --> E["执行工作任务"]
    E --> F{"需要休息?"}

    F -->|"是"| G["移动到休息区"]
    F -->|"否"| H{"工作时间结束?"}

    G --> I["等待休息完成"]
    I --> H

    H -->|"否"| E
    H -->|"是"| J["移动到出口"]
    J --> K["下班"]

    %% 并行数据记录
    D --> L["记录位置数据"]
    E --> M["记录工作数据"]
    G --> N["记录休息数据"]

    %% 安全检查
    E --> O["检查安全装备"]
    O --> P{"发现违规?"}
    P -->|"是"| Q["记录违规数据"]
    P -->|"否"| R["记录正常数据"]

    %% 数据导出
    L --> S["数据导出器"]
    M --> S
    N --> S
    Q --> S
    R --> S
```

### 3.2. 车辆管理模块 (Vehicle Manager)

#### 3.2.1 核心功能
-   **车辆类型管理**: 支持工程车、运输车、混凝土车等基本类型
-   **异步运输流程**: 使用异步函数处理完整的运输流程（装载→运输→卸载→返回）
-   **简单路径控制**: 基于预设路径点的简单移动控制
-   **载重状态管理**: 管理车辆的载重状态和基本属性
-   **数据记录**: 记录车辆位置、状态、载重等数据

### 3.3. 设备管理模块 (Equipment Manager)

#### 3.3.1 核心功能
-   **塔吊模拟**: 使用异步函数处理吊装流程（旋转→起升→移动→下降→复位）
-   **升降机模拟**: 使用异步函数处理楼层服务（移动→开门→等待→关门）
-   **简单运动控制**: 基于Unity Transform的基本运动控制
-   **负载监控**: 简单的负载状态管理
-   **数据记录**: 记录设备状态、位置、负载等数据

### 3.4. 环境监测模块 (Environment Monitor)

#### 3.4.1 核心功能
-   **噪声监测**: 简单的噪声源管理和数据生成
-   **扬尘监测**: 基本的扬尘源管理和数据生成
-   **监测点管理**: 管理环境监测点的位置和数据采集
-   **数据记录**: 生成环境监测数据

### 3.5. 气象模拟模块 (Weather Simulator)

#### 3.5.1 核心功能
-   **异步天气变化**: 使用异步函数处理天气变化流程
-   **基本气象参数**: 温度、湿度、风向、风速等基本参数
-   **天气类型**: 晴天、雨天、多云等基本天气类型
-   **数据记录**: 生成气象数据

### 3.6. 门禁管理模块 (Access Control)

#### 3.6.1 核心功能
-   **出入记录**: 记录人员和车辆的出入信息
-   **身份管理**: 简单的身份识别和记录
-   **统计分析**: 生成出入统计数据
-   **数据记录**: 记录出入事件数据

### 3.7. 物料管理模块 (Material Manager)

#### 3.7.1 核心功能
-   **异步出入库**: 使用异步函数处理物料出入库流程
-   **存储管理**: 管理物料存储区域和状态
-   **物料分类**: 管理不同类型的建筑材料
-   **数据记录**: 记录物料流转数据

### 3.8. 数据导出模块 (Data Exporter)

#### 3.8.1 核心功能
-   **统一数据收集**: 从各个模块收集数据
-   **格式标准化**: 统一的CSV和JSON格式导出
-   **实时记录**: 定时记录各模块状态数据
-   **文件管理**: 自动生成带时间戳的数据文件

---

## 4. 异步编程架构

### 4.1. 异步编程优势

基于异步编程参考规范，本项目采用异步编程模式替代传统状态机，实现以下核心优势：

#### 4.1.1 逻辑聚合性提升
- **完整业务流程**: 将复杂的多步骤操作组织在单一异步方法中
- **线性代码结构**: 代码按时间顺序自然展开，符合人类思维习惯
- **集中式流程控制**: 避免了状态机中分散的状态转换逻辑

#### 4.1.2 代码复杂度简化
- **消除状态管理**: 不再需要维护复杂的状态枚举和状态转换表
- **减少回调嵌套**: 使用 `await` 关键字替代复杂的回调链
- **统一错误处理**: 使用 try-catch 机制集中处理异常情况

#### 4.1.3 维护性增强
- **单点修改**: 业务流程变更只需在一个异步方法中进行
- **调试友好**: 可以在完整流程中设置断点进行调试
- **代码可读性**: 异步代码更接近自然语言描述

### 4.2. UniTask 技术框架

#### 4.2.1 核心要求
- **必须使用 UniTask**: 专为Unity优化的异步库，替代.NET原生Task
- **零GC分配**: 基于结构体和自定义构建器，避免内存分配
- **高性能**: 运行于Unity PlayerLoop，性能优于原生Task
- **深度Unity集成**: 可await所有AsyncOperation和协程

#### 4.2.2 方法与返回类型规范
- **命名规则**: 所有异步方法以 `Async` 结尾（如 `MoveToAsync`）
- **返回类型**: 无返回值用 `UniTask`，有返回值用 `UniTask<T>`
- **禁止使用**: `async void`，以 `async UniTaskVoid` 替代

#### 4.2.3 CancellationToken 使用
- **参数传递**: 长时间运行的方法需接受 `CancellationToken`
- **取消管理**: 通过 `CancellationTokenSource` 取消操作
- **资源清理**: 在 `OnDestroy` 中清理资源

### 4.3. 异步编程实现模式

#### 4.3.1 工人异步工作流程示例
```csharp
public async UniTask ExecuteWorkDayAsync(CancellationToken cancellationToken)
{
    try
    {
        // 等待上班时间
        await WaitForWorkTimeAsync(cancellationToken);

        // 移动到工作区域
        await MoveToWorkAreaAsync(cancellationToken);

        // 工作循环
        while (!IsWorkDayEnd())
        {
            // 执行工作任务
            await ExecuteWorkTaskAsync(cancellationToken);

            // 检查是否需要休息
            if (NeedRest())
            {
                await MoveToRestAreaAsync(cancellationToken);
                await WaitForRestCompleteAsync(cancellationToken);
            }
        }

        // 下班流程
        await MoveToExitAsync(cancellationToken);
    }
    catch (OperationCanceledException)
    {
        // 处理取消操作
        Debug.Log("工作流程被取消");
    }
    catch (Exception ex)
    {
        // 处理其他异常
        Debug.LogError($"工作流程异常: {ex.Message}");
    }
}
```

#### 4.3.2 车辆异步运输流程示例
```csharp
public async UniTask ExecuteTransportTaskAsync(CancellationToken cancellationToken)
{
    try
    {
        // 等待任务分配
        await WaitForTaskAssignmentAsync(cancellationToken);

        // 移动到装载点
        await MoveToLoadPointAsync(cancellationToken);

        // 等待装载完成
        await WaitForLoadingAsync(cancellationToken);

        // 移动到卸载点
        await MoveToUnloadPointAsync(cancellationToken);

        // 等待卸载完成
        await WaitForUnloadingAsync(cancellationToken);

        // 返回待命区域
        await ReturnToStandbyAsync(cancellationToken);
    }
    catch (OperationCanceledException)
    {
        Debug.Log("运输任务被取消");
    }
    catch (Exception ex)
    {
        Debug.LogError($"运输任务异常: {ex.Message}");
    }
}
```

#### 4.3.3 设备异步操作流程示例
```csharp
public async UniTask ExecuteCraneOperationAsync(CancellationToken cancellationToken)
{
    try
    {
        // 旋转到装载位置
        await RotateToLoadPositionAsync(cancellationToken);

        // 下降到装载高度
        await LowerToLoadHeightAsync(cancellationToken);

        // 等待装载完成
        await WaitForLoadingAsync(cancellationToken);

        // 起升到安全高度
        await LiftToSafeHeightAsync(cancellationToken);

        // 旋转到卸载位置
        await RotateToUnloadPositionAsync(cancellationToken);

        // 下降到卸载高度
        await LowerToUnloadHeightAsync(cancellationToken);

        // 等待卸载完成
        await WaitForUnloadingAsync(cancellationToken);

        // 复位到待命状态
        await ResetToStandbyAsync(cancellationToken);
    }
    catch (OperationCanceledException)
    {
        Debug.Log("塔吊操作被取消");
    }
    catch (Exception ex)
    {
        Debug.LogError($"塔吊操作异常: {ex.Message}");
    }
}
```

### 4.4. 异步编程最佳实践

#### 4.4.1 异常处理规范
- **统一处理**: 使用try-catch包围全流程，区分`OperationCanceledException`和普通异常
- **日志记录**: 记录异常信息便于调试和问题追踪
- **优雅降级**: 异常情况下的合理处理和状态恢复

#### 4.4.2 资源管理
- **取消令牌传播**: 在整个异步调用链中正确传播CancellationToken
- **资源清理**: 在OnDestroy中取消所有异步操作，释放CancellationTokenSource
- **内存泄漏防护**: 避免长时间运行的异步操作造成内存泄漏

#### 4.4.3 性能优化
- **零分配优化**: 利用UniTask的零分配特性实现高性能异步操作
- **避免重复await**: 合理缓存异步操作结果，避免重复等待
- **批量操作**: 合并多个异步操作提高效率

---

## 5. 开发规范与总结

### 5.1. 核心设计原则
- **简化优先**: 避免过度工程化，专注业务逻辑实现
- **异步优先**: 使用异步函数替代状态机，保持业务逻辑高内聚
- **直接调用**: 使用传统的方法调用，避免复杂的事件系统
- **数据导向**: 重点关注数据生成和导出，简化非核心功能

### 5.2. 技术要求
- **Unity引擎**: 基于Unity 2018.4.13f1+开发
- **UniTask库**: 必须使用UniTask进行异步编程
- **C# 7.0+**: 支持async/await语法
- **MonoBehaviour**: 直接继承MonoBehaviour，不使用接口和抽象类

### 5.3. 开发流程
1. **需求分析**: 明确业务需求和数据输出要求
2. **模块设计**: 设计简单的模块结构和异步流程
3. **编码实现**: 使用异步函数实现业务逻辑
4. **测试验证**: 编写单元测试验证功能正确性
5. **数据验证**: 确保生成的数据符合预期格式和质量

### 5.4. 项目目标
通过简化的架构设计和异步编程模式，构建一个高效、易维护的建筑工地数字孪生模拟系统，专注于生成高质量的结构化数据，为AI训练和数据分析提供可靠的数据源。

---