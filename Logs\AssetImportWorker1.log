Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.7f1 (13a8ffad9172) revision 1288447'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32673 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-30T07:30:04Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.1.7f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
F:/LongProjects/General Editor
-logFile
Logs/AssetImportWorker1.log
-srvPort
60349
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/LongProjects/General Editor
F:/LongProjects/General Editor
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [12716]  Target information:

Player connection [12716]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3556322728 [EditorId] 3556322728 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-D9EUJSV) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12716] Host joined multi-casting on [***********:54997]...
Player connection [12716] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 262.54 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 1.09 ms.
Initialize engine version: 6000.1.7f1 (13a8ffad9172)
[Subsystems] Discovering subsystems at path E:/Unity/6000.1.7f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/LongProjects/General Editor/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 2070 SUPER (ID=0x1e84)
    Vendor:          NVIDIA
    VRAM:            7989 MB
    App VRAM Budget: 7221 MB
    Driver:          32.0.15.7680
Initialize mono
Mono path[0] = 'E:/Unity/6000.1.7f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.1.7f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.1.7f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56620
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.1.7f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001285 seconds.
- Loaded All Assemblies, in  0.542 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.463 seconds
Domain Reload Profiling: 1004ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (281ms)
		LoadAssemblies (150ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (278ms)
			TypeCache.Refresh (276ms)
				TypeCache.ScanAssembly (200ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (464ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (370ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (54ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (178ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.699 seconds
Refreshing native plugins compatible for Editor in 63.00 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 54.94 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-General Editor
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing KTX for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000b7] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x0002a] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.ktx@9cc7165d9d20/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing Draco for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000b7] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x0002a] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.draco@2ed1ef9a2677/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.316 seconds
Domain Reload Profiling: 3009ms
	BeginReloadAssembly (210ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (80ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (1296ms)
		LoadAssemblies (769ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (656ms)
			TypeCache.Refresh (495ms)
				TypeCache.ScanAssembly (463ms)
			BuildScriptInfoCaches (123ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (1317ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1037ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (169ms)
			ProcessInitializeOnLoadAttributes (629ms)
			ProcessInitializeOnLoadMethodAttributes (230ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 60.21 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.13 ms.
Unloading 407 Unused Serialized files (Serialized files now loaded: 0)
Unloading 11088 unused Assets / (7.5 MB). Loaded Objects now: 11647.
Memory consumption went from 289.9 MB to 282.4 MB.
Total: 13.971900 ms (FindLiveObjects: 1.207600 ms CreateObjectMapping: 2.200800 ms MarkObjects: 6.442000 ms  DeleteObjects: 4.120500 ms)

========================================================================
Received Import Request.
  Time since last request: 23229.503188 seconds.
  path: Assets/Simulation/Scripts/Core/Interfaces/ISimulationEntity.cs
  artifactKey: Guid(d501fbd9b45764d43859dc8b6c205c6b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Core/Interfaces/ISimulationEntity.cs using Guid(d501fbd9b45764d43859dc8b6c205c6b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '566a9e367bb36b0f03bc2d15ef2809ce') in 0.2894553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Simulation/Scripts/Core/Events/EventManager.cs
  artifactKey: Guid(ecc69573a84fabc42803a36960ed942b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Core/Events/EventManager.cs using Guid(ecc69573a84fabc42803a36960ed942b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '384166b7101d65501c1200aa9f4e2c68') in 0.0074792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Simulation/Scripts/SimulatorBase.cs
  artifactKey: Guid(a2b6b3d4ce201ff4ca02c4ef8507f844) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/SimulatorBase.cs using Guid(a2b6b3d4ce201ff4ca02c4ef8507f844) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: 'f383d0111179f6ddc10394275817fe2b') in 0.008869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Simulation/Docs/工作计划书.md
  artifactKey: Guid(f8c0365eaedd1df4aa6f5facb92dfb7a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Docs/工作计划书.md using Guid(f8c0365eaedd1df4aa6f5facb92dfb7a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '7bf5f548255e21dde113500358297a29') in 0.0052793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/GeneralEditor/Scenes/SceneSimulate.unity
  artifactKey: Guid(bacb8c2e31dabb14a8fe04df08c4b5c0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/GeneralEditor/Scenes/SceneSimulate.unity using Guid(bacb8c2e31dabb14a8fe04df08c4b5c0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '2996dafd02119e59a19211b16606250a') in 0.0048841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Simulation/Docs/异步编程参考规范.md
  artifactKey: Guid(6d3aa77c04976b4488f8e744a2a330a8) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Docs/异步编程参考规范.md using Guid(6d3aa77c04976b4488f8e744a2a330a8) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '7824d5ad2f0dfeb999e093c2faf0ea8d') in 0.0040967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/GeneralEditor/Scripts/Components/GeneralTransformSync.cs
  artifactKey: Guid(1485037c1130b5a429c7288edd814752) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/GeneralEditor/Scripts/Components/GeneralTransformSync.cs using Guid(1485037c1130b5a429c7288edd814752) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '6b1ceb09f41a32c16f500d217407c31a') in 0.0050228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Simulation/Scripts/Core/Events/SimulationEvents.cs
  artifactKey: Guid(2cc4e2d5c66dc4943a0d07a0933c075c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Core/Events/SimulationEvents.cs using Guid(2cc4e2d5c66dc4943a0d07a0933c075c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '6f3d48d2701b5a47237755cc355b2a6f') in 0.0047995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Simulation/Scripts/Environment/TimeSystem.cs
  artifactKey: Guid(08eec1ebde9235c4a9d6ae2d6f76187d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Environment/TimeSystem.cs using Guid(08eec1ebde9235c4a9d6ae2d6f76187d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '408ccaf9bbd198cab6ade3b90066af76') in 0.0044802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Simulation/Scripts/README.md
  artifactKey: Guid(8576291e79f922143b13945cb42adddf) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/README.md using Guid(8576291e79f922143b13945cb42adddf) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: 'bd0c04ba0064c6ec221324d10d93a67d') in 0.0039754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.708 seconds
Refreshing native plugins compatible for Editor in 61.47 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing KTX for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.ktx@9cc7165d9d20/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing Draco for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.draco@2ed1ef9a2677/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.444 seconds
Domain Reload Profiling: 3153ms
	BeginReloadAssembly (567ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (178ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (991ms)
		LoadAssemblies (489ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (611ms)
			TypeCache.Refresh (257ms)
				TypeCache.ScanAssembly (237ms)
			BuildScriptInfoCaches (317ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1444ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1164ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (399ms)
			ProcessInitializeOnLoadAttributes (556ms)
			ProcessInitializeOnLoadMethodAttributes (198ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 60.96 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.07 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 11086 unused Assets / (7.1 MB). Loaded Objects now: 11654.
Memory consumption went from 227.5 MB to 220.4 MB.
Total: 12.951800 ms (FindLiveObjects: 1.139300 ms CreateObjectMapping: 1.993400 ms MarkObjects: 5.779700 ms  DeleteObjects: 4.037700 ms)

Prepare: number of updated asset objects reloaded= 0
