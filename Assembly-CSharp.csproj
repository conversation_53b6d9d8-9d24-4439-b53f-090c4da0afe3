﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace>GeneralEditor</RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_7;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;DOTWEEN;UNITASK_DOTWEEN_SUPPORT;ENVIRO_3;ENVIRO_URP;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.7f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\GeneralEditor\Scripts\Models\QualitySafety\InspectionResult.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.MeshRendererSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.BoneWeightSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Test\MQTTTransformSyncDemo.cs" />
    <Compile Include="Assets\Enviro 3 - Sky and Weather\Scripts\ThirdPartySupport\Mirror\EnviroMirrorServer.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Materials\Inventory.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.MaskableGraphic+CullStateChangedEventSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RectSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Security\AlarmEvent.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Vector2Surrogate.cs" />
    <Compile Include="Assets\Enviro 3 - Sky and Weather\Scripts\ThirdPartySupport\WAPI\EnviroWorldAPI.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.CanvasGroupSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TextureSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Materials\MaterialTransaction.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RectIntSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.RuntimeAnimationClipEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.SpriteEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TerrainColliderSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Texture2DSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\GeneralTransformSyncDescriptor.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.VerticalLayoutGroupSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ToggleSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Battlehub.RTEditor.RuntimeAnimationSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEvent\UnityEngine.Events.UnityEventSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Battlehub.RTEditor.GameViewCameraSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Vector2IntSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.FontSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\BusinessEntities\Vehicle.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Importers\FbxImporter.cs" />
    <Compile Include="Assets\Enviro 3 - Sky and Weather\Sample\Scripts\EventTest.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\RawImageDescriptor.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Components\StreamingCamera.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.Dropdown+DropdownEventSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.BoundsSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.VerticalLayoutGroupEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.AudioSourceSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.AudioSourceEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.SelectableEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Color32Surrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\UserManagement\User.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\Battlehub.RTEditor.RuntimeAnimationEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.LightSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ColorBlockSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.MaterialSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.MaskSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.Dropdown+OptionDataSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.HorizontalLayoutGroupSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.DetailPrototypeEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Battlehub.RTEditor.RuntimeAnimationPropertySurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.SpriteStateSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.PhysicMaterialSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.TerrainEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.MeshFilterEnumerator.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\BusinessEntities\VehicleEntryExitLog.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\BuiltinMenu\MenuHelp.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Security\AccessControlPoint.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.RawImageSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.PlaneSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.SpriteSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RectOffsetSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\StreamingCameraDescriptor.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RigidbodySurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.Button+ButtonClickedEventSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Generated\Protobuf\Serializer.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.HorizontalLayoutGroupEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.MaterialEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.BoxColliderSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.GraphicRaycasterSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TreeInstanceSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Ray2DSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.SceneManagement.SceneSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Vector3Surrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.CameraSurrogate.cs" />
    <Compile Include="Assets\Enviro 3 - Sky and Weather\Scripts\ThirdPartySupport\Photon\EnviroPhotonIntegration.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RaySurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.RawImageEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.ButtonEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\TMPro.TMP_FontAssetSurrogate.cs" />
    <Compile Include="Assets\Enviro 3 - Sky and Weather\Sample\Scripts\UISample.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEvent\UnityExtensions.UnityEventPersistentCallSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.NavigationSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.LightBakingOutputSurrogate.cs" />
    <Compile Include="Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\JintSample.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\TMPro.TextMeshProUGUIEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.DropdownEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ShadowSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.MeshFilterSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.GridLayoutGroupSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.CameraEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.TextSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Test\MQTTTransformSync.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Components\GeneralVolume.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\QualitySafety\Inspection.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.AudioListenerSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TerrainSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ScrollRect+ScrollRectEventSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ScrollRectSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\IotManagement\DataStream.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\QualitySafety\NonConformanceReport.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.CapsuleColliderSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.CanvasEnumerator.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\BusinessEntities\EquipmentMaintenanceLog.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Vector4Surrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.MeshSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\Battlehub.RTEditor.RuntimeAnimationPropertyEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.AnimationCurveSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.DetailPrototypeSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.ToggleEnumerator.cs" />
    <Compile Include="Assets\Enviro 3 - Sky and Weather\Scripts\ThirdPartySupport\Mirror\EnviroMirrorPlayer.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.LayerMaskSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.SpriteStateEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ScrollbarSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.CapsuleColliderEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Generated\Protobuf\ModuleDependencies.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\TaskManagement\Task.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TerrainLayerSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.SphereColliderEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.SkinnedMeshRendererEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Generated\RuntimeAssetDatabaseHost.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.QuaternionSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\BusinessEntities\Equipment.cs" />
    <Compile Include="Assets\Battlehub\RTEditor_Data\Scripts\Editors\EditorsMapCreator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.ColliderSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Security\AlertRule.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\GeneralSkyboxDescriptor.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Matrix4x4Surrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Materials\Warehouse.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.GridLayoutGroupEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RaycastHit2DSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.TerrainLayerEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.Events.UnityEventBaseEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ToggleGroupSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.GraphicSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.ShaderSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\GeneralEnviroManagerDescriptor.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\GeneralBehaviour.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.ScrollbarEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Vector3IntSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.Dropdown+OptionDataEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.TextEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\TMPro.TextMeshProUGUISurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Components\GeneralTransformSync.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.CanvasScalerSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Security\AccessLog.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.CanvasSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Battlehub.RTEditor.RuntimeAnimationClipSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.BoxColliderEnumerator.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\UserManagement\RolePermission.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TreePrototypeSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\QualitySafety\InspectionChecklist.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.ImageEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\Battlehub.RTCommon.ExposeToEditorEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.SkyboxSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.MeshColliderSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.Scrollbar+ScrollEventSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.FlareSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.RectMask2DSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.ColorSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Materials\Material.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\BusinessEntities\Team.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\CanvasGroupDescriptor.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Materials\Supplier.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\IotManagement\IotDevice.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.LayoutElementSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.ScrollRectEnumerator.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\CanvasDescriptor.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.KeyframeSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.NavigationEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.MeshRendererEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RectTransformSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Environment\EnvironmentalStation.cs" />
    <Compile Include="Assets\Enviro 3 - Sky and Weather\Scripts\ThirdPartySupport\Microsplat\EnviroMicrosplatIntegration.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\TaskManagement\TaskDependency.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.Events.UnityEventEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ButtonSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\CameraDescriptor.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Security\SecurityCamera.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RaycastHitSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\UserManagement\Role.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.OutlineSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.AnimationTriggersSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.SkyboxEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ContentSizeFitterSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.SphereColliderSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\TaskManagement\TaskComment.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Importers\WavImporter.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TransformSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.BoundsIntSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Importers\Mp3Importer.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\AudioSourceDescriptor.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.TerrainColliderEnumerator.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\QualitySafety\ChecklistItem.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.DropdownSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.ImageSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.TerrainDataSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ContextMenu\HierarchyContextMenu.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.SkinnedMeshRendererSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Hash128Surrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\Environment\EnvironmentalReading.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.SceneEnumerator.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\BusinessEntities\Personnel.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Generated\ObjectEnumeratorFactory.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.UI.GraphicEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.SelectableSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.UI.Toggle+ToggleEventSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\ComponentDescriptors\GeneralVolumeDescriptor.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.RenderTextureSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.TerrainDataEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.LightEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.AudioClipSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.ColliderEnumerator.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\DynamicSurrogates.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Battlehub.RTCommon.ExposeToEditorSurrogate.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\Enumerators\UnityEngine.MeshColliderEnumerator.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEvent\UnityExtensions.UnityEventArgumentsCacheSurrogate.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Components\GeneralSkybox.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\UserManagement\Permission.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Models\UserManagement\Organization.cs" />
    <Compile Include="Assets\GeneralEditor\Scripts\Components\GeneralEnviroManager.cs" />
    <Compile Include="Assets\Battlehub\StorageData\Surrogates\UnityEngine.Audio.AudioMixerGroupSurrogate.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\SelectionPicker.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libspatializer_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\d3d9\libdirect3d9_filters_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libadjust_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_yuy2_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libhqdn3d_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libscene_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_mpjpeg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\librecord_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libplaylist_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\ReflectionProbe\EnviroReflectionProbe.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libspdif_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_nv12_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_dummy_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\version.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libmkv_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libaudioscrobbler_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libxa_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libspudec_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTCommon\Resources\UnlitColorClip.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libequalizer_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Sample\Shader\EnviroTransparentSurface.shader" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsReprojectURP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libtextst_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libscaletempo_plugin.dll" />
    <None Include="Assets\Battlehub\StorageData\Generated\link.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libsdl_image_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile-2-Pass.shader" />
    <None Include="Assets\Fonts\7000常用字.txt" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Fog\EnviroVolumetrics.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libzvbi_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libh26x_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libsubtitle_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\URP17\OutlineComposite.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libtospdif_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libugly_resampler_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libaribcam_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsDepth.shader" />
    <None Include="Assets\Battlehub\README.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libdemux_chromecast_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_vc1_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libsubstx3g_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsReprojectHDRP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\libntservice_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi422_i420_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\Grid.shader" />
    <None Include="Assets\Battlehub\Protobuf.Net\Licence.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libnoseek_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\SkyIncludeHLSL.hlsl" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\NormalsPreview.shader" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Resources\RTBuilder.StringResources.en-US.xml" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libdemux_cdg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libdav1d_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libnuv_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libty_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libadf_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\access\libfilesystem_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libpng_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\gui\libskins2_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Fog\EnviroBilateralBlurURP.shader" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\FaceHighlight.shader" />
    <None Include="Assets\UniversalMediaPlayer\Shaders\UMP-QuadrantCanvas(Linear).shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libschroedinger_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_dirac_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libmotionblur_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\VolumetricCloudsBlendIncludeHLSL.hlsl" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libgrey_yuv_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libprefetch_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libdemux_stl_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTCommon\Resources\UnlitColor.shader" />
    <None Include="Assets\Battlehub\Storage.Core\Resources\IsNormalMapCompute.compute" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libdeinterlace_plugin.dll" />
    <None Include="Assets\Battlehub\RTScripting.Jint\Content\Resources\RTScripting.Jint.StringResources.en-US.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi422_yuy2_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\VertexShader.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libcvdsub_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_mpegvideo_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libt140_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libstereo_widen_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libx26410b_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Shaders\UMP-QuadrantCanvas(Gamma).shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libmad_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libdrawable_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_yuy2_mmx_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libyuy2_i422_plugin.dll" />
    <None Include="Assets\Battlehub\RTScripting.Common\Content\FontFiles\OFL.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libparam_eq_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libinvert_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libjpeg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\librotate_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\liblpcm_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libcdg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libsid_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libuleaddvaudio_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libwebvtt_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Blit\EnviroBlitTroughHDRP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\UniversalMediaPlayer.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsReproject.shader" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsRaymarchHDRP.shader" />
    <None Include="Assets\Battlehub\RTScripting.Common\ThirdParty\System.Runtime.CompilerServices.Unsafe.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\liboldrc_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroWeatherMap.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Shaders\UMP-EquirectangularCanvas.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libwave_plugin.dll" />
    <None Include="Assets\Battlehub\Storage.Core\Resources\HasTransparencyCompute.compute" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libstats_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\BoxSelectionShader.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTCommon\Resources\RenderTextureOverlay.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\liblogger_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_copy_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Sky\EnviroSkybox.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libtransform_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libdxva2_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libtwolame_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_output\libamem_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\libdummy_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Blit\EnviroBlitThroughURP17.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\gui\libqt_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libaudio_format_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libsepia_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Prefabs\Legacy\Views\Resources\TemplateWindow.cs.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libmft_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libvorbis_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTGizmos\Shaders\Resources\Handles.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Prefabs\Models\Materials\Shaders\DepthMaskColorBillboardShader.shader" />
    <None Include="Assets\GeneralEditor\Resources\RTTerrain.StringResources.cn.xml" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsRaymarchURP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libaudiobargraph_a_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Particles\EnviroLightning.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libanaglyph_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libopus_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libfingerprinter_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libantiflicker_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Fog\EnviroHeightFogHDRP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libfluidsynth_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Fog\EnviroBilateralBlur.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Prefabs\Resources\TemplateViewModel.cs.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libspatialaudio_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libinflate_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Shaders\UMP-ARVideoCanvas.shader" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\LineBillboard.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libdirect3d9_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_mixer\libfloat_mixer_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\VertexPicker.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\access\libhttps_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_asf_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditorDemo\Content\Runtime\Utils\Dispatcher\Dispatcher Documentation.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\libvlc.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libasf_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libremap_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Prefabs\Legacy\Views\Resources\RegisterTemplateWindow.cs.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_ogg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libsubsusf_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libscte27_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libdirect3d11_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libyuy2_i420_plugin.dll" />
    <None Include="Assets\Battlehub\RTScripting.Common\Content\Resources\RTScripting.StringResources.en-US.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libwgl_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libvmem_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libpva_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\URP17\OutlineBlur.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libflaschen_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libcroppadd_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libreal_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\librawaud_plugin.dll" />
    <None Include="Assets\Battlehub\Protobuf.Net\protobuf-net.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libbluescreen_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libalphamask_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libaom_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libpsychedelic_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_rgb_mmx_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_mp4_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libgaussianblur_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\SDFFunctions.hlsl" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libcc_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libblendbench_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libfps_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_ps_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\ParticlesInclude.cginc" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Prefabs\Models\Materials\Shaders\ArrowShader.shader" />
    <None Include="Assets\Battlehub\RTEditor\ThirdParty\UnityWeld\UnityWeld.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsDepthHDRP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libnsv_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libvod_rtsp_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\liboggspots_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsBlendHDRP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\liblibass_plugin.dll" />
    <None Include="Assets\GeneralEditor\Resources\RTScripting.StringResources.cn.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\liblibmpeg2_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\FacePicker.shader" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Sky\EnviroSkyboxHDRP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\libhotkeys_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libes_plugin.dll" />
    <None Include="Assets\Battlehub\Protobuf.Net\WSA\protobuf-net.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libimage_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_10_p010_plugin.dll" />
    <None Include="Assets\GeneralEditor\Resources\RTBuilder.StringResources.cn.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libexport_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libqsv_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\OutlineComposite.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_rgb_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\OutlineBlur.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Prefabs\Models\Materials\Shaders\DepthMaskColorShader.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libcache_block_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditorDemo\Content\Runtime\RTEditor\BundledObject\NewSurfaceShader.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libcompressor_plugin.dll" />
    <None Include="Assets\Battlehub\StorageData\Generated\StorageTypeModel.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libwinhibit_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libedgedetection_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTCommon\Resources\Billboard.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\LineBillboard.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libyuv_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libdmo_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libsmf_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Prefabs\Resources\TemplateView.cs.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libscte18_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\librtpvideo_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Fonts\FontLicense!.txt" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\OutlinePrepass.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\d3d11\libdirect3d11_filters_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_avi_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\VolumetricCloudsInclude.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\text_renderer\libfreetype_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libdvbsub_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libyuvp_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Fog\EnviroVolumetricsURP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libedummy_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\UniversalMediaPlayer\Shaders\UMP-VideoSphericalCanvas.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Prefabs\Models\Materials\Shaders\SSCircleShader.shader" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\URP17\Outline.hlsl" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libmotiondetect_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\SmoothingPreview.shader" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\VolumetricCloudsTexURPInclude.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libmpgv_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libwav_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\SkyInclude.cginc" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\ReflectionProbe\EnviroCubemapBlur.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libswscale_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libspeex_resampler_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libtrivial_channel_mixer_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libstl_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libblend_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi422_yuy2_sse2_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Prefabs\Models\Materials\Shaders\AxisShader.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libaddonsvorepository_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Fog\EnviroHeightFogURP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\libwin_hotkeys_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libgain_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libchorus_flanger_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Particles\EnviroWeatherParticles.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Prefabs\AnimationEditor\TimelineControl\Resources\TimelineGrid.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libmp4_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libflacsys_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libdiracsys_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libchain_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_h264_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTDeformer\Resources\RTDeformer.StringResources.en-US.xml" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libtta_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libogg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libwingdi_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\librawdv_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Blit\EnviroBlitThroughURP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\libwin_msg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libdirectdraw_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libps_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libgradient_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\liboldmovie_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libdca_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\liberase_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Resources\RTEditor.StringResources.en-US.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\librawvid_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libcaca_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\text_renderer\libsapi_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroApplyShadows.shader" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\PointBillboard.shader" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\MeshEditor\Resources\FaceHighlight.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libg711_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libd3d11va_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\VertexColorClipUsingClipPlane.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\access\libudp_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Prefabs\Models\Materials\Shaders\SSQuadShader.shader" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\FogInclude.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libball_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libgrain_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libvobsub_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libvoc_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\NoiseInclude.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libaraw_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libfaad_plugin.dll" />
    <None Include="Assets\Battlehub\Third-Party Notices.txt" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libmjpeg_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_rgb_sse2_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libmagnify_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\text_renderer\libtdummy_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\Shape.shader" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsBlendURP.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_hevc_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\libvlccore.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libskiptags_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libvhs_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_a52_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\VolumetricCloudsBlendInclude.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libripple_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libspeex_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libsharpen_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libx264_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Shaders\UMP-TransparentCanvas.shader" />
    <None Include="Assets\Battlehub\RTScripting.Roslyn\Content\Resources\RTScripting.Roslyn.StringResources.en-US.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libmod_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\access\libhttp_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\VolumetricCloudsTexInclude.cginc" />
    <None Include="Assets\GeneralEditor\Resources\RTEditor.StringResources.cn.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libflac_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Prefabs\Models\Materials\Shaders\QuadShader.shader" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\Outline.hlsl" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\librv32_plugin.dll" />
    <None Include="Assets\Battlehub\RTScripting.Common\ThirdParty\System.Reflection.Metadata.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libvpx_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libposterize_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_wav_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTCommon\Resources\Point.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libdolby_surround_decoder_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_mpeg4audio_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libx265_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTCommon\Resources\LineBillboard.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Prefabs\AnimationEditor\TimelineControl\Resources\UnlitTextureInstanced.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\Tools\ObjectWireframe\Resources\LineBillboard.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libddummy_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_flac_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\VolumetricCloudsTexHDRPInclude.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libnsc_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsDepthURP.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\UIControls\DockPanels\Shaders\DepthMask.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Outline\Resources\UnityOutline.cginc" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\libnetsync_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libadpcm_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libcache_read_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libkate_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libglwin32_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTTerrain\Resources\RTTerrain.StringResources.en-US.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libkaraoke_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_mpeg4video_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTCommon\Resources\SceenSpaceDecal.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libaribsub_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libscaletempo_pitch_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Includes\FogIncludeHLSL.hlsl" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\mux\libmux_ts_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libaes3_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libcolorthres_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_output\libdirectsound_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libsimple_channel_mixer_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\access\liblive555_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libmpc_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroApplyShadowsHDRP.shader" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroApplyShadowsURP.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libdirectory_demux_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libaiff_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libsvcdsub_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi420_yuy2_sse2_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libadaptive_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libnormvol_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTTerrain\Prefabs\Materials\TerrainHandleShader.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libxml_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\librawvideo_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Integration\Shaders\EdgePicker.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTEditor\Prefabs\Resources\RegisterTemplateWindow.cs.txt" />
    <None Include="Assets\Battlehub\RTEditor\ThirdParty\ICSharpCode.SharpZipLib.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libau_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\stream_filter\libhds_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libgl_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libgnutls_plugin.dll" />
    <None Include="Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Jint.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libavi_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libmono_plugin.dll" />
    <None Include="Assets\Simulation\Docs\原始需求.txt" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTEditor\Resources\UIForeground.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_mlp_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libmpg123_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libextract_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libgme_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditorURP\Content\Runtime\RTHandles\Outline\URP17\OutlineMask.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\RawImage.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libpostproc_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libtheora_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\misc\libaddonsfsstorage_plugin.dll" />
    <None Include="Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Acornima.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libvc1_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_output\libvdummy_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_mixer\libinteger_mixer_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libsubsdec_plugin.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libcrystalhd_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libttml_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libfreeze_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Outline\Resources\UnityOutline.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libts_plugin.dll" />
    <None Include="Assets\Battlehub\RTExtensions\Content\Runtime\RTBuilder\Resources\Materials\PBTexturePreview.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libgradfun_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libcaf_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Fog\EnviroHeightFog.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libsamplerate_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\access\librtp_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_mpegaudio_plugin.dll" />
    <None Include="Assets\Battlehub\RTScripting.Common\ThirdParty\System.Collections.Immutable.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libscale_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libpuzzle_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsRaymarch.shader" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\LineBillboardClip.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\demux\libdemuxdump_plugin.dll" />
    <None Include="Assets\GeneralEditor\Resources\RTScripting.Roslyn.StringResources.cn.xml" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_chroma\libi422_yuy2_mmx_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_dts_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\libavcodec_plugin.dll" />
    <None Include="Assets\Battlehub\RTEditor\Content\Runtime\RTHandles\Shaders\Resources\PointBillboard.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libcanvas_plugin.dll" />
    <None Include="Assets\GeneralEditor\Resources\RTDeformer.StringResources.cn.xml" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\packetizer\libpacketizer_av1_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\control\libgestures_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\video_filter\libmirror_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\audio_filter\libheadphone_channel_mixer_plugin.dll" />
    <None Include="Assets\UniversalMediaPlayer\Plugins\Win\x86_64\plugins\codec\liba52_plugin.dll" />
    <None Include="Assets\Enviro 3 - Sky and Weather\Resources\Shader\Clouds\EnviroVolumetricCloudsBlend.shader" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Ply">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Ply.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LibTessDotNet">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Dependencies\LibTessDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.HDRLoader">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.HDRLoader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="IxMilia.ThreeMf">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Dependencies\IxMilia.ThreeMf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.SharpZipLib">
      <HintPath>Library\PackageCache\com.unity.sharp-zip-lib@6b61f82b0cb3\Runtime\Unity.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Textures">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Textures.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.ThreeMf">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.ThreeMf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SafeStbImageSharp">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Dependencies\SafeStbImageSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="protobuf-net">
      <HintPath>Assets\Battlehub\Protobuf.Net\protobuf-net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Stl">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Stl.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityWeld">
      <HintPath>Assets\Battlehub\RTEditor\ThirdParty\UnityWeld\UnityWeld.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Fbx">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Fbx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="StorageTypeModel">
      <HintPath>Assets\Battlehub\StorageData\Generated\StorageTypeModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Gltf.Draco">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Gltf.Draco.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Gltf">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Gltf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Obj">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Obj.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TriLibCore.Dae">
      <HintPath>Assets\TriLib\TriLibCore\Plugins\Debug\TriLibCore.Dae.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets\Battlehub\RTEditor\ThirdParty\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Jint">
      <HintPath>Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Jint.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Acornima">
      <HintPath>Assets\Battlehub\RTScripting.Jint\ThirdParty\Jint\Acornima.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PsdPlugin">
      <HintPath>Library\ScriptAssemblies\PsdPlugin.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Path.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Common.Path.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="glTFast.dots">
      <HintPath>Library\ScriptAssemblies\glTFast.dots.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx">
      <HintPath>Library\ScriptAssemblies\Autodesk.Fbx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>Library\ScriptAssemblies\PPv2URPConverters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder.AddOns.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.AddOns.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.SharpZipLib.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.SharpZipLib.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InternalAPIEngineBridge.001">
      <HintPath>Library\ScriptAssemblies\Unity.InternalAPIEngineBridge.001.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Tilemap.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder.AssetIdRemapUtility">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.AssetIdRemapUtility.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualEffectGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualEffectGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="glTFast.Animation">
      <HintPath>Library\ScriptAssemblies\glTFast.Animation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Profiling.Core">
      <HintPath>Library\ScriptAssemblies\Unity.Profiling.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Updater">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.Updater.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx.Editor">
      <HintPath>Library\ScriptAssemblies\Autodesk.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Settings.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Settings.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Draco">
      <HintPath>Library\ScriptAssemblies\Draco.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor.ConversionSystem">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.Editor.ConversionSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Psdimporter.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Psdimporter.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="glTFast.Editor">
      <HintPath>Library\ScriptAssemblies\glTFast.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask">
      <HintPath>Library\ScriptAssemblies\UniTask.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Common.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ResourceManager">
      <HintPath>Library\ScriptAssemblies\Unity.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Draco.Editor">
      <HintPath>Library\ScriptAssemblies\Draco.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\UniTask.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="glTFast">
      <HintPath>Library\ScriptAssemblies\glTFast.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.Linq">
      <HintPath>Library\ScriptAssemblies\UniTask.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InternalAPIEditorBridge.001">
      <HintPath>Library\ScriptAssemblies\Unity.InternalAPIEditorBridge.001.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ktx">
      <HintPath>Library\ScriptAssemblies\Ktx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.SharpZipLib.Utils">
      <HintPath>Library\ScriptAssemblies\Unity.SharpZipLib.Utils.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.DOTween">
      <HintPath>Library\ScriptAssemblies\UniTask.DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualEffectGraph.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.VisualEffectGraph.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Common.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Formats.Fbx.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.Addressables">
      <HintPath>Library\ScriptAssemblies\UniTask.Addressables.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Formats.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Simulation.csproj" />
    <ProjectReference Include="Battlehub.RTExtensions.csproj" />
    <ProjectReference Include="com.Tivadar.Best.MQTT.csproj" />
    <ProjectReference Include="IngameDebugConsole.Runtime.csproj" />
    <ProjectReference Include="Battlehub.RTEditor.csproj" />
    <ProjectReference Include="Battlehub.LoadImageAsync.csproj" />
    <ProjectReference Include="Battlehub.RTEditor.Demo.csproj" />
    <ProjectReference Include="Battlehub.Storage.Runtime.csproj" />
    <ProjectReference Include="Battlehub.RTScripting.Jint.csproj" />
    <ProjectReference Include="Battlehub.RTEditor.Editor.csproj" />
    <ProjectReference Include="Battlehub.RTImporter.csproj" />
    <ProjectReference Include="CodeAnalysis.csproj" />
    <ProjectReference Include="Battlehub.RTEditor.URP.Editor.csproj" />
    <ProjectReference Include="Battlehub.Storage.Editor.csproj" />
    <ProjectReference Include="Battlehub.RTScripting.Roslyn.csproj" />
    <ProjectReference Include="Battlehub.RTScripting.Jint.Editor.csproj" />
    <ProjectReference Include="Battlehub.Storage.Addressables.csproj" />
    <ProjectReference Include="Battlehub.RTExtensions.URP.csproj" />
    <ProjectReference Include="IngameDebugConsole.Editor.csproj" />
    <ProjectReference Include="com.Tivadar.Best.WebSockets.csproj" />
    <ProjectReference Include="UMP.csproj" />
    <ProjectReference Include="Battlehub.RTScripting.Common.csproj" />
    <ProjectReference Include="com.Tivadar.Best.HTTP.csproj" />
    <ProjectReference Include="Battlehub.Storage.Core.Editor.csproj" />
    <ProjectReference Include="Enviro3.Editor.csproj" />
    <ProjectReference Include="Battlehub.Storage.Core.Runtime.csproj" />
    <ProjectReference Include="Battlehub.RTExtensions.URP.Editor.csproj" />
    <ProjectReference Include="com.Tivadar.Best.TLSSecurity.csproj" />
    <ProjectReference Include="Battlehub.RTExtensions.Editor.csproj" />
    <ProjectReference Include="TriLib.Editor.csproj" />
    <ProjectReference Include="HSVPicker.csproj" />
    <ProjectReference Include="TriLib.csproj" />
    <ProjectReference Include="Battlehub.RTScripting.Roslyn.Editor.csproj" />
    <ProjectReference Include="Battlehub.Storage.ShaderUtil.Editor.csproj" />
    <ProjectReference Include="UIShapesKit.Editor.csproj" />
    <ProjectReference Include="DOTween.Modules.csproj" />
    <ProjectReference Include="Battlehub.Storage.ShaderUtil.Runtime.csproj" />
    <ProjectReference Include="UIShapesKit.csproj" />
    <ProjectReference Include="Simulation.Tests.csproj" />
    <ProjectReference Include="Enviro3.Runtime.csproj" />
    <ProjectReference Include="Battlehub.RTEditor.URP.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
