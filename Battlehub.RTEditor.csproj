﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Battlehub.RTEditor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_7;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;DOTWEEN;UNITASK_DOTWEEN_SUPPORT;ENVIRO_3;ENVIRO_URP;UNITY_ADDRESSABLES;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.7f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="E:\Unity\6000.1.7f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\UnityWeld\VirtualizingTreeViewBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IAssetDatabaseProjectExtension.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetDatabaseModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\BoxSelectionRenderer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\IProject.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\CustomSelectionComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\SaveSceneDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Outline\IOutlineManager.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Tooltip\TooltipUI.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingTreeViewItem.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetDatabaseViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RuntimeHighlightComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\TreeView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileFooterViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\ComponentFactoryModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\ItemDropMarker.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\SaveFileViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IContextMenuModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\DragDrop.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\CameraComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\Dopesheet.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\MenuItemInfoToMenuItemViewModelAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\PlayerPrefsLayoutStorageModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\ManageProjectsViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\LayoutGroupComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\SelectionComponentState.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\ListBoxItem.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\SpriteGizmoManager.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\EnumEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Input\InputLow.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\ManageProjectsView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\TreeViewDataModel\TreeElementUtility.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RuntimeWindow.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\ListEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\MeshRendererComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Tooltip\Tooltip.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\Vector4Editor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\StringEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Utils\UnitsConverter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\AssetLibrarySelectDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Views\MobileAddComponentView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RuntimeSelection.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\ControlsMap.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\ToggleComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\ProgressBar.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\RigidbodyComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Dialog\DialogManager.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\TimelinePointer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\RectTransformChangeListener.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\AssetLibraryImportDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Input\InputLowForRDP.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\HideOnRegionResizing.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\GroupingModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\CustomHandleExtension.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Run.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RotationHandleModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\ContextMenuView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\ConsoleViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\ImportFileDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\Tab.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\MultiToggle\MultiToggle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\MeshCombiner\MeshUtils.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\CustomTypeEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\Expander.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\ProjectItemIconSelector.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\SettingsViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\AnchorPresetSelector.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\SelectColorViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ThemeAsset.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\ViewBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\ViewInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\QuaternionEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\RectTransformEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingScrollRect.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\CancelArgs.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\ProjectsDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\Error.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\UnityWeld\SetIsActiveBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\RepeatButton.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\TreeViewDropMarker.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\DepthMaskingBehavior.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Commands\EditCmd.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\SaveAssetViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\InputDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\IsActiveProperty.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\SceneGrid.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\MultiToggle\MultiToggleLayer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\PersistentCallEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Utils\Splash.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\TransformUtility.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\ImageComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\MaterialEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Dispatcher.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetDatabaseImportSources\IAssetDatabaseImportSourceModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\TransformChangesTracker.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileContextPanelViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Views\MobileMenuView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RuntimeShaderProfilesAsset.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\BoxColliderGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\ILayoutStorageModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\InspectorViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\IntEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\HeaderPanel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\IPersistentSurrogate.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\XmlSerializerExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\GameView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\RTEGraphicsLayer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\BuiltinMenu\MenuHelp.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Strong.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\AudioReverbZoneGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\GraphicsUtility.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Colors.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\SphereGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\SphereColliderComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\IsActivePropertyBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\Input\BaseGizmoInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\SaveAssetView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\MeshesCache.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Localization\Localization.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetDatabaseTreeViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\AssetDatabaseImportToggle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Workspace.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileContextMenu.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUIAttributes.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RTSLPath.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Reflection.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\PointerEventDataExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\SelectableComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\RenderTextureCamera.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\APIExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\GameObjectEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\HVLayoutGroupComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\DragAndDropListener.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\IStorageAsync.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUIControls.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\LayersEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\ObjectWireframe\ObjectWireframe.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\SaveFileDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\BoxColliderComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\AssetLibrarySelectView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileHeaderViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\BuiltinMenu\GameObjectsAsset.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RuntimeTools.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\Legacy\IEnumerableToIEnumerableOfAssetLibraryAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\LockAxes.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\Examples\MasterDetailsViewModelExample.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\SelectColorDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\WindowManager.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\HierarchicalDataView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RectTool.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\CapsuleColliderGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\TreeViewExpander.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\Legacy\ProjectListLegacyModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\TimelineTextPanel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUIExtension.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\ScaleHandle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\ReferenceToBoolAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\Resizer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IGroupingModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\HingeJointComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\PointLightGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileLayoutInit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\SaveFileView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Utils\IOC.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Controls\MobileContextPanelToggle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\SettingsComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\MethodCaller.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Menu\MenuItem.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Input\ProjectFolderViewInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\GridLayoutGroupComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\RuntimeSelectionInputBase.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\Legacy\AssetLibraryImportToggle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\AssetDatabaseSelectView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\RTEAppearance.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\AnimationSelectPropertiesDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Utils\DialogViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ProjectViewImpl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Menu\UIMenuStyle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\SpriteGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\MeshFilterComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\IEnumerableToIEnumerableOfConsoleLogEntryAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\UIStyle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Controls\MobileCreatorTrigger.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\IEnumerableToIEnumerableOfExposeToEditorAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\TabPanelScroller.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\BHRoot.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Utils\LowMemoryTrigger.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\HierarchyViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\PlacementModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\DragField.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\BoundsEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RenderPipelineInfo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\BuiltinMenu\MenuWindow.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\ReflectActiveState.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\ProjectTreeViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\ConeGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Views\MobileComponentView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\HeaderLabel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\BoolEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\AssetEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RotationHandle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\DialogViewModelSettingsToDialogBindingSettingsAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RTSLIgnore.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Controls\PropertyEditors\MobileVector2Editor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\RectTransformGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUI.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\ImportFileView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\OpenFileViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\PositionHandleModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\AssetDatabaseView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\SceneGizmoInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\TimelineBoxSelection.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\AnimationViewImpl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\UnityWeld\Adapters\IListOfStringToListOfTMP_DropdownOptionData.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\AddComponentControl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Input\RuntimeEditorInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\AssetLibrarySelectViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\GameObjectToHierarchyTextColorAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\CanvasComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileEditorsInit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\ScrollbarResizer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Views\MobileHeaderView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IProjectListModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\RTEGraphics.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\SaveSceneViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RuntimeScene.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\IEditorsMap.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\ViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Input\RTEBaseInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ColliderEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Attributes\ReadonlyAttribute.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Menu\Menu.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Tooltip\ToggleTooltip.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\ProjectFolderViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\GameObjectExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\ProjectListModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\RuntimeToolsInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileCreatorViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\DragDropTarget.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ConsoleView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetDatabaseImportSources\ResourcesImportSourceModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUIBinder.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\AssetDatabaseSaveView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RTE.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ProjectTreeView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\HierarchyViewImpl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Menu\ContextMenuTrigger.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\Legacy\IEnumerableToIEnumerableOfProjectItemAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\LocalizedTooltip.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingItemsControl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\ListBox.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\ConsoleView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\TransformExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\LightComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\BaseHandleInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\SelectObjectView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\LogTypeToSpriteAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Lock.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\CapsuleGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ProjectFolderViewImpl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetDatabaseImportSources\AddressablesImportSourceModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\BaseGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\UnityEventBaseEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\EventHandlerExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\AudioListenerComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\InputProviderAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\OptionsEditorBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\IRuntimeSceneManager.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\InspectorView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Utils\MenuItemViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\BusyIndicator.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\ImportAssetToImportStatusAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RenderPipelineSetActive.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\ProjectView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\RTECamera.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\SelectObjectDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\RuntimeAnimationComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\FourFloatEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingListBoxItem.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ProjectTreeViewImpl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RTEUIRaycaster.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\TreeViewDataModel\TreeModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\ToolCmdItemViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\ToolsView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Utils\IRTEState.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\BuiltinMenu\MenuFile.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\BoolToImageAlphaConverter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\ISerializer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\ProjectFolderView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\ExposeToEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\SliderOverride.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RuntimeObjects.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileMenuViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\Legacy\ProjectItemAdapterOptions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\RuntimeSelectionInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\CreateSceneLayer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\TMP_InputFieldScrollFix.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\PathHelper.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\AnimationView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ConsoleFooter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\MaterialDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RuntimeSceneComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\RegionPopupBehaviour.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\Legacy\IEnumerableToIEnumerableOfProjectInfoAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\InspectorModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\FlagsIntEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\EditorsMap.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Models\MobileEditorModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileAddComponentViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\RangeIntEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Mobile\MobileSceneControls.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AnimationViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetThumbnailUtil.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\Legacy\PlayerPrefsLegacyStorageModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileHierarchyWindowInit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\ScrollbarClampValues.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUILayout.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ProjectFolderView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\IPlayerPrefsStorage.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\CursorHelper.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Dialog\Dialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Controls\MobileContextPanel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\AssetLibraryImportView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IPlacementModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\ViewModelBase.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RTSLSettings.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\UnityWeld\SelectableBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\MeshColliderComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\RenderersCache.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\StandardMaterialComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RTESceneWindow.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\TreeViewItem.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\ScaleHandleModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\IEnumerableToIEnumerableOfAssetViewModelAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\UnityEventHelper.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Views\MobileGameObjectView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\AnimationView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\SceneSettingPanel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\ObjectToTexture\TakeSnapshot.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\AnimationComponentView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\BillboardFacingCamera.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\Legacy\AssetLibraryImportStatus.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingTreeViewDropMarker.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\RangeEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingItemDropMarker.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileAnimationWindowInit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\GameObjectEditorUtils.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\ItemContainer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RTEVersion.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RuntimeShaderUtil.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\ProjectViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileImporterViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Input\SceneViewInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\SphereColliderGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\SpringJointComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\RegionPopupBackground.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\RTEDeps.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\BaseHandleModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingListBox\UnityWeld\VirtualizingListBoxBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Outline\Custom\ICustomOutlinePrepass.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\ColorEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RTSLAdditive.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\BuiltinMenu\HideBuiltinMenu.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\EditorsMapStorage.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\BoolFloatEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingItemContainer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Input\VRPointer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\SelectionPicker.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\RuntimeGameWindow.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\Region.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\SpotlightGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\LayerMaskEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\AnimationPropertiesView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\EatDragEvent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\ObjectToTexture\TextureScale.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AboutViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\MouseOrbit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Utils\InputViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RuntimeTextAsset.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\AssetDatabaseImportView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\TimelineControl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\ToolCmdItem.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Controls\MobileContextPanelPositionUpdater.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\SettingsDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\TreeViewDataModel\TreeElement.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\LayoutElementResizer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\SceneViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\OpenFileDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RuntimeSelectionComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\SaveAssetDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\DockPanels\DockPanel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\StandardMaterialUtils.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\TransformEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\IEnumerableToIEnumerableOfImportAssetAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\AnchorPreset.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\EatPointerEvents.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileGameObjectViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\SceneViewImpl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileMainMenuDefinition.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\DelayedTextUpdater.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetDatabaseImportSources\ResourcesLoaderModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\ITypeMap.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\IListEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\EditorPresenterInputFieldBehaviour.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\Vector2Editor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Menu\MenuCreator.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\TexturePickerDropArea.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetObjectModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\Vector3Editor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\ObjectWireframe\InstanceIDToColor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IInspectorModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\AssetLibraryInfo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\LightGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Outline\OutlineEffect.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Utils\CreateEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Dialogs\AboutDialog.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\LogTypeToColorAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\SkinnedMeshRendererGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\BaseHandle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\AssetDatabaseTreeView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RuntimeSelectionComponentUI.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\UnityWeld\ControlBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\AnimationSelectPropertiesView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\AudioSourceComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\RuntimeEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUIBuilder.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUIPropertyEditors.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\PointerEnterExitListener.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\BoxGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\TexturePicker.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingItemsControlInputProvider.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\MultiToggle\MultiTogglePanel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingListBox.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ScriptableObjectEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Tooltip\TooltipLayer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileSceneWindowInit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Input\Pointer.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\SkinnedMeshRendererComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\LayoutElementComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\ImporterModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\SceneSettingsComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\PositionHandleInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\TimelineControlInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RTSLInclude.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\RectTransformExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\IEnumerableToIEnumerableOfImportGroupAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Controls\MobileContextMenuTrigger.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\Examples\SimpleProceduralExample.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\SaveSceneView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\BoxSelection.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ContextMenu.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Layouts\BuiltInWindows.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\PropertyChangedEventHandler.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\RTSLDeps.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\ScrollRectSync.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\ProjectItem.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\GameViewCamera.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\RuntimeSceneMobileInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\RuntimeSceneInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetDatabaseImportSources\AddressablesLoaderModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\VirtualizingTreeView\VirtualizingTreeView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\SceneView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\SettingsView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Views\MobileCreatorView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\ObjectEditorClickHandler.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\ResourcePreviewUtility.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\MultiselectDropDown\MultiselectDropdown.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\UnityWeld\TypeResolverEx.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Input\ProjectTreeViewInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\CapsuleColliderComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\ActivateInputFieldOnBeginEdit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\TimelineGrid.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\HierarchyView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\RectTransformComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetDatabaseBaseViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\InspectorView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\SelectInputFieldOnStart.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\TreeView\ItemsControl.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\TransformChildrenChangeListener.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\BuiltinMenu\MenuGameObject.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\ImportFileViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IAssetDatabaseModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\SelectColorView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\Controls\PropertyEditors\MobileVector3Editor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\ITypeModelCreator.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\SceneGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\TextMeshProUGUIComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\MobileProjectWindowInit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Localization\LocalizedText.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\OptionsEditorString.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\Legacy\ProjectItemToImportStatusAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\IComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ToolsPanel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\IImporterModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoUI\AutoUIControlsExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\PositionHandle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\ToggleGroupComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RuntimeHandlesHitTester.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Outline\Custom\CustomOutlinePrepass.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RTEStandaloneInputModule.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\ProjectItemView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\OptionsEditorFloat.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\ConsoleCounterToStringAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Outline\OutlineManager.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Input\HierarchyViewInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\ValueChangedArgs.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Utils\IResourcePreviewUtilityExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Utils\IOCGroup.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\View.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\HierarchyView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\PropertyEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\FileBrowser.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\ButtonComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\RuntimeAnimation.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetDatabaseImportViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RuntimeBinaryAsset.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\AnimationPropertyView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\AssetLibraryImportViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetDatabaseImportSourceViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\ObjectEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\HierarchicalDataViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\Repeater.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\ToolsViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\FixedJointComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\AnimationCreateView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\GameObjectToHierarchyIconAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\FloatEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\MeshCombiner\Scripts\MeshUtils.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Commands\GameObjectCmd.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\IProjectAsync.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\AudioSourceGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetDatabaseSaveViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RuntimeCameraWindow.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\LogTypeAdapterOptions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Legacy\ProjectTreeView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Views\ProjectView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Input\RuntimeUndoInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\ArrayEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RTEComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\AnimationTimelineView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\PrefabSpawnPoint.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\OpenFileView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Mobile\MobileSceneInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RuntimeUndo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\PropertyEditorBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\SceneView.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\GameObjectToHierarchyIconAdapterOptions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\RTEBase.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Common\InputProvider.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\LayersInfo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Binding\Adapters\Legacy\ProjectItemToSpriteAdapter.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Legacy\Input\BaseViewInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Menu\MainMenuButton.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Views\Utils\AssetDatabaseImportStatus.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\RuntimeHandlesComponent.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PrefabCommandsEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\Legacy\AssetLibrariesImportSourceModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\TextureExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\Legacy\SelectObjectViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\EditorExtension.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\AssetDatabaseImportSources\IExternalAssetLoaderModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\OptionsEditor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\GizmoUtility.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\ObjectToTexture\ObjectToTexture.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\WindowOverlay.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\MathHelper.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\FloatValidator.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\UIControls\Dialog\UnityWeld\DialogBinding.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AnimationSelectPropertiesViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTGizmos\DirectionalLightGizmo.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\UnityEventBaseExtensions.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Models\Legacy\AssetDatabaseOverRTSLModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Tools\MeshCombiner\PivotEditor\Scripts\MeshUtils.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Mobile\ViewModels\MobileComponentViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\RuntimeConsole.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\BuiltinMenu\MenuEdit.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\PropertyEditors\ObjectEditorLoader.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AnimationEditor\TimelineControl\TimelineText.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\IUnityObjectFactory.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\TransformComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\ViewModels\AssetDatabaseSelectViewModel.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\Utils\Attributes\FieldDisplayNameAttribute.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\Editors\ComponentDescriptors\UI\TextComponentDescriptor.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTHandles\Input\BoxSelectionInput.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\UIControls\UIEditorStyle.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTEditor\AutoSaveLayout.cs" />
    <Compile Include="Assets\Battlehub\RTEditor\Runtime\RTSL\Interface\RTSLVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\Battlehub\RTEditor\Runtime\Battlehub.RTEditor.asmdef" />
    <None Include="Assets\Battlehub\RTEditor\Runtime\RTCommon\Graphics\Resources\UI-Default2020.shader" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityWeld">
      <HintPath>Assets\Battlehub\RTEditor\ThirdParty\UnityWeld\UnityWeld.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets\Battlehub\RTEditor\ThirdParty\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="protobuf-net">
      <HintPath>Assets\Battlehub\Protobuf.Net\protobuf-net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>E:\Unity\6000.1.7f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ResourceManager">
      <HintPath>Library\ScriptAssemblies\Unity.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="HSVPicker.csproj" />
    <ProjectReference Include="Battlehub.LoadImageAsync.csproj" />
    <ProjectReference Include="Battlehub.Storage.ShaderUtil.Runtime.csproj" />
    <ProjectReference Include="Battlehub.Storage.Core.Runtime.csproj" />
    <ProjectReference Include="Battlehub.Storage.Runtime.csproj" />
    <ProjectReference Include="Battlehub.Storage.Addressables.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
