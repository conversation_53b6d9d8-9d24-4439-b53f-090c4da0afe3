{"format": 1, "restore": {"f:\\LongProjects\\General Editor\\Assembly-CSharp.csproj": {}}, "projects": {"f:\\LongProjects\\General Editor\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "f:\\LongProjects\\General Editor\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Demo.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Demo.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTImporter.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTImporter.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.MQTT.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.MQTT.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.TLSSecurity.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.TLSSecurity.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj"}, "f:\\LongProjects\\General Editor\\DOTween.Modules.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\DOTween.Modules.csproj"}, "f:\\LongProjects\\General Editor\\Enviro3.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Enviro3.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}, "f:\\LongProjects\\General Editor\\IngameDebugConsole.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Editor.csproj"}, "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Simulation.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Simulation.csproj"}, "f:\\LongProjects\\General Editor\\Simulation.Tests.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Simulation.Tests.csproj"}, "f:\\LongProjects\\General Editor\\TriLib.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\TriLib.csproj"}, "f:\\LongProjects\\General Editor\\TriLib.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\TriLib.Editor.csproj"}, "f:\\LongProjects\\General Editor\\UIShapesKit.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\UIShapesKit.csproj"}, "f:\\LongProjects\\General Editor\\UIShapesKit.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\UIShapesKit.Editor.csproj"}, "f:\\LongProjects\\General Editor\\UMP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\UMP.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj", "projectName": "Battlehub.LoadImageAsync", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.LoadImageAsync\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj", "projectName": "Battlehub.RTEditor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTEditor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Demo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Demo.csproj", "projectName": "Battlehub.RTEditor.Demo", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Demo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTEditor.Demo\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Editor.csproj", "projectName": "Battlehub.RTEditor.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTEditor.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj", "projectName": "Battlehub.RTEditor.URP", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTEditor.URP\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.Editor.csproj", "projectName": "Battlehub.RTEditor.URP.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTEditor.URP.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj", "projectName": "Battlehub.RTExtensions", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTExtensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj", "projectName": "Battlehub.RTExtensions.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTExtensions.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj", "projectName": "Battlehub.RTExtensions.URP", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTExtensions.URP\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.Editor.csproj", "projectName": "Battlehub.RTExtensions.URP.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTExtensions.URP.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTImporter.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTImporter.csproj", "projectName": "Battlehub.RTImporter", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTImporter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTImporter\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj", "projectName": "Battlehub.RTScripting.Common", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTScripting.Common\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj", "projectName": "Battlehub.RTScripting.Jint", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTScripting.Jint\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.Editor.csproj", "projectName": "Battlehub.RTScripting.Jint.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTScripting.Jint.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj", "projectName": "Battlehub.RTScripting.Roslyn", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTScripting.Roslyn\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.Editor.csproj", "projectName": "Battlehub.RTScripting.Roslyn.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTScripting.Roslyn.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj", "projectName": "Battlehub.Storage.Addressables", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Addressables\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj", "projectName": "Battlehub.Storage.Core.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Core.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj", "projectName": "Battlehub.Storage.Core.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Core.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj", "projectName": "Battlehub.Storage.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj", "projectName": "Battlehub.Storage.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj", "projectName": "Battlehub.Storage.ShaderUtil.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.ShaderUtil.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj", "projectName": "Battlehub.Storage.ShaderUtil.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.ShaderUtil.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj", "projectName": "CodeAnalysis", "projectPath": "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\CodeAnalysis\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj", "projectName": "com.Tivadar.Best.HTTP", "projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\com.Tivadar.Best.HTTP\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.MQTT.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.MQTT.csproj", "projectName": "com.Tivadar.Best.MQTT", "projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.MQTT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\com.Tivadar.Best.MQTT\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.TLSSecurity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.TLSSecurity.csproj", "projectName": "com.Tivadar.Best.TLSSecurity", "projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.TLSSecurity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\com.Tivadar.Best.TLSSecurity\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj", "projectName": "com.Tivadar.Best.WebSockets", "projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\com.Tivadar.Best.WebSockets\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\DOTween.Modules.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\DOTween.Modules.csproj", "projectName": "DOTween.Modules", "projectPath": "f:\\LongProjects\\General Editor\\DOTween.Modules.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\DOTween.Modules\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Enviro3.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Enviro3.Editor.csproj", "projectName": "Enviro3.Editor", "projectPath": "f:\\LongProjects\\General Editor\\Enviro3.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Enviro3.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj", "projectName": "Enviro3.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Enviro3.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\HSVPicker.csproj", "projectName": "HSVPicker", "projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\HSVPicker\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\IngameDebugConsole.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Editor.csproj", "projectName": "IngameDebugConsole.Editor", "projectPath": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\IngameDebugConsole.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj", "projectName": "IngameDebugConsole.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\IngameDebugConsole.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Simulation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Simulation.csproj", "projectName": "Simulation", "projectPath": "f:\\LongProjects\\General Editor\\Simulation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Simulation\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Simulation.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Simulation.Tests.csproj", "projectName": "Simulation.Tests", "projectPath": "f:\\LongProjects\\General Editor\\Simulation.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Simulation.Tests\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Simulation.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Simulation.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\TriLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\TriLib.csproj", "projectName": "TriLib", "projectPath": "f:\\LongProjects\\General Editor\\TriLib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\TriLib\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\TriLib.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\TriLib.Editor.csproj", "projectName": "TriLib.Editor", "projectPath": "f:\\LongProjects\\General Editor\\TriLib.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\TriLib.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\TriLib.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\TriLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\UIShapesKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\UIShapesKit.csproj", "projectName": "UIShapesKit", "projectPath": "f:\\LongProjects\\General Editor\\UIShapesKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\UIShapesKit\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\UIShapesKit.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\UIShapesKit.Editor.csproj", "projectName": "UIShapesKit.Editor", "projectPath": "f:\\LongProjects\\General Editor\\UIShapesKit.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\UIShapesKit.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\UIShapesKit.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\UIShapesKit.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\UMP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\UMP.csproj", "projectName": "UMP", "projectPath": "f:\\LongProjects\\General Editor\\UMP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\UMP\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}